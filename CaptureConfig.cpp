#include "CaptureConfig.h"
#include <wx/filename.h>
#include <wx/stdpaths.h>
#include "Logger.h"

CaptureConfig::CaptureConfig()
    : m_config(nullptr)
{
    SetDefaults();
}

CaptureConfig::~CaptureConfig()
{
    if (m_config)
    {
        delete m_config;
        m_config = nullptr;
    }
}

bool CaptureConfig::LoadConfig(const wxString& configFile)
{
    if (m_config)
    {
        delete m_config;
        m_config = nullptr;
    }

    // 确定配置文件的实际路径，按优先级查找
    wxString actualConfigPath = DetermineConfigPath(configFile);

    if (!wxFileName::FileExists(actualConfigPath))
    {
        // 配置文件不存在，使用默认值并在项目目录创建配置文件
        LogMessage("配置文件不存在，使用默认配置: " + actualConfigPath.ToStdString());
        return SaveConfig(actualConfigPath);
    }

    m_config = new wxFileConfig(wxEmptyString, wxEmptyString, actualConfigPath);
    if (!m_config)
    {
        LogError("无法打开配置文件: " + actualConfigPath.ToStdString());
        return false;
    }

    // 读取摄像头配置
    m_cameraIP = m_config->Read("/Camera/IP", m_cameraIP);
    m_cameraPort = m_config->ReadLong("/Camera/Port", m_cameraPort);
    m_cameraUsername = m_config->Read("/Camera/Username", m_cameraUsername);
    m_cameraPassword = m_config->Read("/Camera/Password", m_cameraPassword);
    m_cameraResolution = m_config->Read("/Camera/Resolution", m_cameraResolution);
    m_cameraFrameRate = m_config->ReadLong("/Camera/FrameRate", m_cameraFrameRate);

    // 读取拍照配置
    m_cameraDistance = m_config->ReadDouble("/Capture/CameraDistance", m_cameraDistance);
    m_safetyBuffer = m_config->ReadDouble("/Capture/SafetyBuffer", m_safetyBuffer);
    m_photoQuality = m_config->ReadLong("/Capture/PhotoQuality", m_photoQuality);
    m_autoCaptureEnabled = m_config->ReadBool("/Capture/AutoCaptureEnabled", m_autoCaptureEnabled);
    m_photoRootPath = m_config->Read("/Capture/PhotoRootPath", m_photoRootPath);

    LogMessage("配置文件加载成功: " + actualConfigPath.ToStdString());
    // 打印绝对路径
    wxFileName fileName(actualConfigPath);
    fileName.MakeAbsolute();
    LogMessage("配置文件绝对路径: " + fileName.GetFullPath().ToStdString());

    return true;
}

bool CaptureConfig::SaveConfig(const wxString& configFile)
{
    if (m_config)
    {
        delete m_config;
        m_config = nullptr;
    }

    // 确定配置文件的实际路径，优先保存到项目目录
    wxString actualConfigPath = DetermineConfigPath(configFile);

    m_config = new wxFileConfig(wxEmptyString, wxEmptyString, actualConfigPath);
    if (!m_config)
    {
        LogError("无法创建配置文件: " + actualConfigPath.ToStdString());
        return false;
    }

    // 保存摄像头配置
    m_config->Write("/Camera/IP", m_cameraIP);
    m_config->Write("/Camera/Port", (long)m_cameraPort);
    m_config->Write("/Camera/Username", m_cameraUsername);
    m_config->Write("/Camera/Password", m_cameraPassword);
    m_config->Write("/Camera/Resolution", m_cameraResolution);
    m_config->Write("/Camera/FrameRate", (long)m_cameraFrameRate);

    // 保存拍照配置
    m_config->Write("/Capture/CameraDistance", m_cameraDistance);
    m_config->Write("/Capture/SafetyBuffer", m_safetyBuffer);
    m_config->Write("/Capture/PhotoQuality", (long)m_photoQuality);
    m_config->Write("/Capture/AutoCaptureEnabled", m_autoCaptureEnabled);
    m_config->Write("/Capture/PhotoRootPath", m_photoRootPath);

    // 强制写入文件
    m_config->Flush();

    LogMessage("配置文件保存成功: " + actualConfigPath.ToStdString());
    return true;
}

CaptureConfig CaptureConfig::GetDefaultConfig()
{
    CaptureConfig config;
    config.SetDefaults();
    return config;
}

void CaptureConfig::SetDefaults()
{
    // 摄像头默认配置
    m_cameraIP = "************";
    m_cameraPort = 8000;
    m_cameraUsername = "admin";
    m_cameraPassword = "Tck123456";
    m_cameraResolution = "2560x1440";
    m_cameraFrameRate = 30;

    // 拍照默认配置
    m_cameraDistance = 2.0f;
    m_safetyBuffer = 0.5f;
    m_photoQuality = 95;
    m_autoCaptureEnabled = true;
    m_photoRootPath = "photos";
}

wxString CaptureConfig::DetermineConfigPath(const wxString& configFile)
{
    // 如果传入的是绝对路径，直接使用
    wxFileName fileName(configFile);
    if (fileName.IsAbsolute())
    {
        LogMessage("使用绝对路径配置文件: " + configFile.ToStdString());
        return configFile;
    }

    // 1. 首选：项目根目录中的配置文件
    wxString projectPath = wxGetCwd(); // 获取当前工作目录（项目根目录）
    wxString projectConfigPath = wxFileName(projectPath, configFile).GetFullPath();

    if (wxFileName::FileExists(projectConfigPath))
    {
        LogMessage("找到项目目录配置文件: " + projectConfigPath.ToStdString());
        return projectConfigPath;
    }

    // 2. 备选：用户数据目录中的配置文件
    wxStandardPaths& stdPaths = wxStandardPaths::Get();
    wxString userDataDir = stdPaths.GetUserDataDir();
    wxString userConfigPath = wxFileName(userDataDir, configFile).GetFullPath();

    if (wxFileName::FileExists(userConfigPath))
    {
        LogMessage("找到用户数据目录配置文件: " + userConfigPath.ToStdString());
        LogMessage("建议将配置文件移动到项目目录: " + projectConfigPath.ToStdString());
        return userConfigPath;
    }

    // 3. 如果都不存在，优先在项目目录创建
    LogMessage("配置文件不存在，将在项目目录创建: " + projectConfigPath.ToStdString());
    return projectConfigPath;
}
