#include "BeltFrame.h"
#include "Logger.h"

//(*InternalHeaders(BeltFrame)
#include <wx/intl.h>
#include <wx/string.h>
//*)
#include <wx/xrc/xmlres.h>
#include <wx/dcmemory.h>
#include <wx/msgdlg.h>
#include "TCK_W_Client_IoTApp.h"

#include "TCK_WR_LANG.h"

extern bool g_dbg_main;

///是否存储MRT实时流数据
static bool g_is_save_mrt_flow = false;

extern char g_iot_server_ip[40];
extern unsigned short g_view_server_port;
extern unsigned short g_view_alive_time;

extern TcpClientWeb *g_tcp_web;

///MRT屏显长度
extern int g_mrt_onescreen_len;
///实时曲线显示方式，0=波形图，1=模拟图，0x02=模拟图使用真实钢芯数
extern int g_curve_display_mode;
///视觉/纵撕实时流帧率
extern int g_vis_frame_rate;

//(*IdInit(BeltFrame)
const long BeltFrame::ID_PANEL1 = wxNewId();
const long BeltFrame::ID_TIMER1 = wxNewId();
//*)

// 异步初始化事件ID
const long BeltFrame::ID_CAMERA_INIT_COMPLETE = wxNewId();
const long BeltFrame::ID_STATUS_HIDE_TIMER = wxNewId();

BEGIN_DECLARE_EVENT_TYPES()
	DECLARE_EVENT_TYPE(wxEVT_ViewDisconnect, 201)
    DECLARE_EVENT_TYPE(wxEVT_Selected_Reply, TcpClientView::Evt_Selected)
    DECLARE_EVENT_TYPE(wxEVT_MRT_Flow, TcpClientView::Evt_MRT_Flow)
    DECLARE_EVENT_TYPE(wxEVT_VIC_Flow, TcpClientView::Evt_VIC_Flow)
    DECLARE_EVENT_TYPE(wxEVT_Tear_Flow, TcpClientView::Evt_Tear_Flow)
    DECLARE_EVENT_TYPE(wxEVT_FindJtOrBsq, TcpClientView::Evt_FindJtOrBsq)
    DECLARE_EVENT_TYPE(wxEVT_Flaw_Max5, TcpClientView::Evt_Flaw_Max5)
    DECLARE_EVENT_TYPE(wxEVT_JT_Max5, TcpClientView::Evt_JT_Max5)
    DECLARE_EVENT_TYPE(wxEVT_Flaw_Lev6, TcpClientView::Evt_Flaw_Lev6)
    DECLARE_EVENT_TYPE(wxEVT_Detect_Mileage, TcpClientView::Evt_DetectMileage)
END_DECLARE_EVENT_TYPES()

DEFINE_EVENT_TYPE(wxEVT_ViewDisconnect)
DEFINE_EVENT_TYPE(wxEVT_Selected_Reply)
DEFINE_EVENT_TYPE(wxEVT_MRT_Flow)
DEFINE_EVENT_TYPE(wxEVT_VIC_Flow)
DEFINE_EVENT_TYPE(wxEVT_Tear_Flow)
DEFINE_EVENT_TYPE(wxEVT_FindJtOrBsq)
DEFINE_EVENT_TYPE(wxEVT_Flaw_Max5)
DEFINE_EVENT_TYPE(wxEVT_JT_Max5)
DEFINE_EVENT_TYPE(wxEVT_Flaw_Lev6)
DEFINE_EVENT_TYPE(wxEVT_Detect_Mileage)

#define EVT_ViewDisconnect(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_ViewDisconnect, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_Selected_Reply(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_Selected_Reply, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_MRT_Flow(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_MRT_Flow, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_VIC_Flow(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_VIC_Flow, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_Tear_Flow(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_Tear_Flow, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_FindJtOrBsq(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_FindJtOrBsq, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_Flaw_Max5(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_Flaw_Max5, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_JT_Max5(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_JT_Max5, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_Flaw_Lev6(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_Flaw_Lev6, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),
#define EVT_Detect_Mileage(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_Detect_Mileage, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),

BEGIN_EVENT_TABLE(BeltFrame,wxFrame)
	//(*EventTable(BeltFrame)
	//*)
	EVT_ViewDisconnect(wxID_ANY, BeltFrame::OnViewDisconnect)
	EVT_Selected_Reply(wxID_ANY, BeltFrame::OnSelectedReply)
	EVT_MRT_Flow(wxID_ANY, BeltFrame::OnMRTFlow)
	EVT_VIC_Flow(wxID_ANY, BeltFrame::OnVICFlow)
	EVT_Tear_Flow(wxID_ANY, BeltFrame::OnTearFlow)
	EVT_FindJtOrBsq(wxID_ANY, BeltFrame::OnFindJtOrBsq)
	EVT_Flaw_Max5(wxID_ANY, BeltFrame::OnFlawMax5)
	EVT_JT_Max5(wxID_ANY, BeltFrame::OnJTMax5)
	EVT_Flaw_Lev6(wxID_ANY, BeltFrame::OnFlawLev6)
	EVT_Detect_Mileage(wxID_ANY, BeltFrame::OnDetectMileage)
	EVT_MENU(ID_CAMERA_INIT_COMPLETE, BeltFrame::OnCameraInitComplete)
	EVT_TIMER(ID_STATUS_HIDE_TIMER, BeltFrame::OnStatusHideTimer)
END_EVENT_TABLE()

extern void TCKFontScale(wxWindow *win);
extern void TCKFontScale(wxFont& font);
extern unsigned short g_language;

BeltFrame::BeltFrame(wxWindow* parent, StationWin *stw, bool onlyone, wxWindowID id,const wxPoint& pos,const wxSize& size)
: m_parent(parent), m_stw(stw)
{
	TCKFontScale(this);

	//(*Initialize(BeltFrame)
	Create(parent, id, wxEmptyString, wxDefaultPosition, wxDefaultSize, wxDEFAULT_FRAME_STYLE|wxFRAME_NO_TASKBAR|wxFRAME_FLOAT_ON_PARENT, _T("id"));
	SetClientSize(wxSize(1080,608));
	Move(wxDefaultPosition);
	Panel1 = new wxPanel(this, ID_PANEL1, wxPoint(0,0), wxSize(1080,608), wxTAB_TRAVERSAL, _T("ID_PANEL1"));
	Timer1.SetOwner(this, ID_TIMER1);
	Center();

	Panel1->Connect(wxEVT_ERASE_BACKGROUND,(wxObjectEventFunction)&BeltFrame::OnPanel1EraseBackground,0,this);
	Panel1->Connect(wxEVT_SIZE,(wxObjectEventFunction)&BeltFrame::OnPanel1Resize,0,this);
	Connect(ID_TIMER1,wxEVT_TIMER,(wxObjectEventFunction)&BeltFrame::OnTimer1Trigger);
	Connect(wxID_ANY,wxEVT_CLOSE_WINDOW,(wxObjectEventFunction)&BeltFrame::OnClose);
	//*)

	if(onlyone)
	{
		SetWindowStyleFlag(wxDEFAULT_FRAME_STYLE|wxFRAME_FLOAT_ON_PARENT);
	}

	///关联热键
	int idf = wxNewId();
    Connect(idf,wxEVT_COMMAND_MENU_SELECTED,(wxObjectEventFunction)&BeltFrame::OnHotKeyF1);
    wxAcceleratorEntry entries[1];
    entries[0].Set(wxACCEL_NORMAL, WXK_F1, idf);
    wxAcceleratorTable accel(1, entries);
    SetAcceleratorTable(accel);

	m_query_flaw_dlg = NULL;
	m_query_joint_dlg = NULL;
	m_query_alarm_dlg = NULL;
	m_report_dlg = NULL;
	m_query_tear_dlg = NULL;

	m_query_flaw_dlg_zx = NULL;
	m_report_dlg_zx = NULL;
	m_stat_dlg = NULL;
	m_query_alarm_dlg_zx = NULL;

	m_save_mrt_flow_fd = NULL;

	///标题
	wxCSConv csc("utf-8");
    wxString strName = csc.cMB2WX(stw->name);
	SetLabel(strName);
    wxString titleStr;
    if(stw->st_type == 5)
		titleStr = _("钢丝绳芯输送带在线实时安全监测系统");
	else
		titleStr = _("钢丝绳在线实时安全监测系统");
    wxFont textFont(18,wxSWISS,wxFONTSTYLE_NORMAL,wxBOLD,false,_("微软雅黑"));
    if(g_language != LANG_CHINESE)
		textFont.SetPointSize(16);
    TCKFontScale(textFont);
    wxColour textColor(*wxWHITE);
    m_title = new TransStaticText(Panel1, titleStr, textFont, textColor, wxPoint(310, 8), wxSize(464, 44));

    if(g_language != LANG_CHINESE)
		textFont.SetPointSize(14);
	else
		textFont.SetPointSize(16);
    TCKFontScale(textFont);
    m_name = new TransStaticText(Panel1, strName, textFont, textColor, wxPoint(866, 10), wxSize(200, 32));

	m_size.SetWidth(1080);
	m_size.SetHeight(608);
	m_unit_size.SetWidth(250);
	m_unit_size.SetHeight(200);
	m_curve_size.SetWidth(548);
	m_curve_size.SetHeight(480);

	///背景图
	m_bgBitmap = wxXmlResource::Get()->LoadBitmap(wxT("bg"));

	if(g_language != LANG_CHINESE)
		textFont.SetPointSize(11);
	else
		textFont.SetPointSize(12);
	TCKFontScale(textFont);
	int appoint = 0;
	int bmpWH = 28;

	wxString strText = _("五处最大损伤");
	m_txt_flawMax5 = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(8+(m_unit_size.GetWidth()-130)/2, 94), wxSize(130, 40));
	if(stw->reg_state & 0x01)
	{
		if(stw->run_state & 0x06)
			appoint = 4;
		else if(stw->run_state & 0x20)
			appoint = 3;
		else if(stw->run_state & 0x10)
			appoint = 2;
		else
			appoint = 1;
	}
	else
		appoint = 5;
	m_tsb_flaw = new TransStaticBmp(Panel1, wxPoint(24, 94+(40-bmpWH)/2), wxSize(bmpWH, bmpWH), appoint, &m_bgBitmap);
	m_tsb_flaw->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnAlarmDClick, NULL, this);
	m_chart_flawMax5 = new BeltChart(Panel1, wxID_ANY, wxPoint(8, 134), m_unit_size);
	m_chart_flawMax5->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnFlawDClick, NULL, this);

	strText = _("损伤分级统计");
	m_txt_flawLev = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(8+(m_unit_size.GetWidth()-200)/2, 342), wxSize(200, 40));
	m_chart_flawLev = new BeltChart(Panel1, wxID_ANY, wxPoint(8, 382), m_unit_size);
	m_chart_flawLev->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnReportDClick, NULL, this);

	if(stw->st_type == 5)
	{
		m_txt_vis = NULL;
		m_chart_vis = NULL;
		m_txt_run = NULL;
		m_view_run = NULL;

		strText = _("五处最大抽动");
		m_txt_jointMax5 = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth() + (m_unit_size.GetWidth()-130)/2, 94), wxSize(130, 40));
		if(stw->reg_state & 0x01)
		{
			if(stw->run_state & 0x06)
				appoint = 4;
			else if(stw->run_state & 0x80)
				appoint = 3;
			else if(stw->run_state & 0x40)
				appoint = 2;
			else
				appoint = 1;
		}
		else
			appoint = 5;
		m_tsb_joint = new TransStaticBmp(Panel1, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth() + 16, 94+(40-bmpWH)/2), wxSize(bmpWH, bmpWH), appoint, &m_bgBitmap);
		m_tsb_joint->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnAlarmDClick, NULL, this);
		m_chart_jointMax5 = new BeltChart(Panel1, wxID_ANY, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth(), 134), m_unit_size);
		m_chart_jointMax5->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnJointDClick, NULL, this);

		strText = _("纵撕实时图像");
		m_txt_tear = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth() + (m_unit_size.GetWidth()-130)/2, 342), wxSize(130, 40));
		if(stw->reg_state & 0x04)
		{
			if(stw->run_state & 0x08)
				appoint = 4;
			else if(stw->run_state & 0x200)
				appoint = 3;
			else if(stw->run_state & 0x100)
				appoint = 2;
			else
				appoint = 1;
		}
		else
			appoint = 5;
		m_tsb_tear = new TransStaticBmp(Panel1, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth() + 16, 342+(40-bmpWH)/2), wxSize(bmpWH, bmpWH), appoint, &m_bgBitmap);
		m_tsb_tear->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnTearAbnDClick, NULL, this);
		m_chart_tear = new BeltChart(Panel1, wxID_ANY, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth(), 382), m_unit_size);
		m_chart_tear->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnTearDClick, NULL, this);
	}
	else
	{
		m_txt_jointMax5 = NULL;
		m_tsb_joint = NULL;
		m_chart_jointMax5 = NULL;

		m_txt_tear = NULL;
		m_tsb_tear = NULL;
		m_chart_tear = NULL;

		strText = _("运行动态");
		m_txt_run = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth() + (m_unit_size.GetWidth()-130)/2, 94), wxSize(130, 40));
		m_view_run = new RunningView(Panel1, wxID_ANY, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth(), 134), m_unit_size);
		m_view_run->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnRunningDClick, NULL, this);

		strText = _("视觉实时图像");
		m_txt_vis = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth() + (m_unit_size.GetWidth()-130)/2, 342), wxSize(130, 40));
		m_chart_vis = new BeltChart(Panel1, wxID_ANY, wxPoint(m_size.GetWidth() - 8 - m_unit_size.GetWidth(), 382), m_unit_size);
		m_chart_vis->Connect(wxEVT_LEFT_DCLICK, (wxObjectEventFunction)&BeltFrame::OnVisDClick, NULL, this);
	}

	m_tsb_disconnect = new TransStaticBmp(Panel1, wxPoint(853,20), wxSize(32,32), wxXmlResource::Get()->LoadBitmap(wxT("discon")), &m_bgBitmap);
	m_tsb_disconnect->Hide();

	if(stw->st_type == 5)
	{
		if(g_curve_display_mode & 0x02)
			m_curve = new BeltCurve(Panel1, stw->mrt_param.sensor_cnt*50, stw->rope_info.count, stw->run_data.jt_num[0], wxID_ANY, wxPoint(8*2+m_unit_size.GetWidth(), 118), m_curve_size);
		else if(g_curve_display_mode == 1)
			m_curve = new BeltCurve(Panel1, stw->mrt_param.sensor_cnt*50, 0, stw->run_data.jt_num[0], wxID_ANY, wxPoint(8*2+m_unit_size.GetWidth(), 118), m_curve_size);
		else
			m_curve = new BeltCurve(Panel1, 0, wxID_ANY, wxPoint(8*2+m_unit_size.GetWidth(), 118), m_curve_size, &m_bgBitmap);
	}
	else
	{
		m_curve = new BeltCurve(Panel1, stw->rope_info.count, wxID_ANY, wxPoint(8*2+m_unit_size.GetWidth(), 118), m_curve_size, &m_bgBitmap);
	}

	textFont.SetPointSize(10);
	TCKFontScale(textFont);
	textColor.SetRGB(0xE1FFE1);
	if((stw->reg_state & 0x01) == 0)
	{
		strText = _("状态：离线");
	}
	else if(stw->run_state & 0x01)
	{
		strText = _("状态：停止检测");
	}
	else if(stw->st_type == 5)
	{
		if(stw->run_data.jt_num[0])
		{
			strText = wxString::Format(_("找到接头：%d/%d  [%s]"), stw->run_data.joint, stw->mrt_param.jt_cnt, stw->run_data.jt_num);
		}
		else
		{
			strText = _("正在寻找特征接头");
		}
	}
	else
	{
		strText = _("状态：检测中");
	}
	m_txt_state = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(m_curve->GetPosition().x, m_curve->GetPosition().y - 32), wxSize(m_curve_size.GetWidth()/3, 32), &m_bgBitmap);
	strText = wxString::Format(_("位置：%.0fm"), stw->run_data.pos);
	m_txt_pos = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(m_curve->GetPosition().x + m_curve_size.GetWidth()/3, m_curve->GetPosition().y - 32), wxSize(m_curve_size.GetWidth()/3, 32), &m_bgBitmap);
	strText = wxString::Format(_("速度：%.1fm/s"), stw->run_data.speed);
	m_txt_speed = new TransStaticText(Panel1, strText, textFont, textColor, wxPoint(m_curve->GetPosition().x + m_curve_size.GetWidth()*2/3, m_curve->GetPosition().y - 32), wxSize(m_curve_size.GetWidth()/3, 32), &m_bgBitmap);

	///实时流TCP连接
	m_tcp_view = new TcpClientView(g_view_alive_time);
	m_tcp_view->SetEvtHandler(this);
	wxString ip = g_iot_server_ip;
    if(ip == wxEmptyString)
        ip = _("127.0.0.1");
    int port_view = g_view_server_port;
    if(port_view == 0)
		port_view = 9200;
	if(!m_tcp_view->Connect(ip.ToStdString().c_str(), port_view))
	{
		wxMessageBox(wxString::Format(_("Connection to server <view> [%d] failed !"), port_view), _("Prompt"));
		return;
	}
	m_view_selected = false;
	TcpClientView::DataEvtSelected evtSel;
	memset(&evtSel, 0, sizeof(TcpClientView::DataEvtSelected));
	strncpy(evtSel.sn, stw->sn, 64);
	strncpy(evtSel.node, stw->node, 16);
	evtSel.isRunningDt = 0x0E;
	if(stw->st_type == 5 && g_curve_display_mode > 0)
		evtSel.isMRTrtdt = 2;
	else
		evtSel.isMRTrtdt = 1;
	if(stw->st_type == 5)
		evtSel.isVICrtdt = 0x02;
	else
		evtSel.isVICrtdt = 0x01;
	evtSel.isArrange = 1;
	evtSel.screenLength = g_mrt_onescreen_len;
	if(stw->st_type != 5 && stw->rope_info.count > 1)
		evtSel.pixel_w = m_curve_size.GetWidth() - 30;
	else
		evtSel.pixel_w = m_curve_size.GetWidth();
	evtSel.pixel_h = m_curve_size.GetHeight() - 22 - 8;
	evtSel.stm_frame = g_vis_frame_rate;
	evtSel.stm_scale = 2;
	m_tcp_view->SendEvent(TcpClientView::Evt_Selected, &evtSel);
	Timer1.Start(1000, false);

	// 初始化接头拍照控制器（仅创建对象，不连接摄像头）
	m_jointCaptureController = new JointCaptureController();
	m_cameraInitState = CAMERA_NOT_INIT;
	m_initThread = nullptr;

	// 初始化状态隐藏定时器
	m_statusHideTimer = new wxTimer(this, ID_STATUS_HIDE_TIMER);

	// 添加摄像头状态显示文本
	wxFont statusFont(10, wxSWISS, wxFONTSTYLE_NORMAL, wxNORMAL, false, _("微软雅黑"));
	TCKFontScale(statusFont);
	m_cameraStatusText = new TransStaticText(Panel1, _("摄像头未初始化"), statusFont,
		wxColour(255, 255, 0), wxPoint(10, 580), wxSize(200, 20), &m_bgBitmap);

	// 启动异步初始化
	InitializeCameraAsync();

	// 初始化圈数管理器
	m_circleManager = nullptr;
	try
	{
		m_circleManager = new CircleManager();

		// 设置皮带长度（从stw->rope_info.len获取）
		if (stw && stw->rope_info.len > 0)
		{
			m_circleManager->SetBeltLength(stw->rope_info.len);
			LogMessage(wxString::Format("设置皮带长度: %.1f米", stw->rope_info.len).ToStdString());
		}
		else
		{
			LogWarning("皮带长度信息无效，使用默认值");
		}

		LogMessage("圈数管理器初始化成功");
	}
	catch (const std::exception& e)
	{
		LogError("圈数管理器初始化失败: " + std::string(e.what()));
		if (m_circleManager)
		{
			delete m_circleManager;
			m_circleManager = nullptr;
		}
	}
	catch (...)
	{
		LogError("圈数管理器初始化失败: 未知异常");
		if (m_circleManager)
		{
			delete m_circleManager;
			m_circleManager = nullptr;
		}
	}
}

BeltFrame::~BeltFrame()
{
	// 停止并清理状态隐藏定时器
	if (m_statusHideTimer) {
		m_statusHideTimer->Stop();
		delete m_statusHideTimer;
		m_statusHideTimer = nullptr;
	}

	// 等待初始化线程完成
	if (m_initThread) {
		if (m_initThread->joinable()) {
			m_initThread->join();
		}
		delete m_initThread;
		m_initThread = nullptr;
	}

	// 清理接头拍照控制器
	if (m_jointCaptureController)
	{
		delete m_jointCaptureController;
		m_jointCaptureController = nullptr;
	}

	// 清理圈数管理器
	if (m_circleManager)
	{
		delete m_circleManager;
		m_circleManager = nullptr;
	}

	//(*Destroy(BeltFrame)
	//*)
}

void BeltFrame::OnClose(wxCloseEvent& event)
{
	Timer1.Stop();
	if(m_save_mrt_flow_fd)
	{
		fflush(m_save_mrt_flow_fd);
		fclose(m_save_mrt_flow_fd);
		m_save_mrt_flow_fd = NULL;
		FILE *fdr = fopen(wxString::Format("mrt_flow_pts-chan%d.dat", m_save_mrt_flow_chan).ToStdString().c_str(), "rb");
		FILE *fdw = fopen(wxString::Format("mrt_flow_chan%d-pts.dat", m_save_mrt_flow_chan).ToStdString().c_str(), "wb");
		if(fdr && fdw)
		{
			std::vector<long long> chanData[MAX_SENSOR_COUNT];
			long long buf[MAX_SENSOR_COUNT];
			while(fread(buf, sizeof(long long), m_save_mrt_flow_chan, fdr) > 0)
			{
				for(int i=0; i<m_save_mrt_flow_chan; i++)
					chanData[i].push_back(buf[i]);
			}
			for(int i=0; i<m_save_mrt_flow_chan; i++)
			{
				for(int j=0; j<chanData[i].size(); j++)
					fwrite(&chanData[i][j], sizeof(long long), 1, fdw);
			}
			fflush(fdw);
		}
		if(fdr)
			fclose(fdr);
		if(fdw)
			fclose(fdw);
	}
	if(m_stw)
		m_stw->frame = NULL;
	if(m_tcp_view)
	{
		delete m_tcp_view;
		m_tcp_view = NULL;
	}
	event.Skip();
	if(m_parent && !m_parent->IsShown())
		m_parent->Close();
}

///F1打开主Main窗口
void BeltFrame::OnHotKeyF1(wxCommandEvent& event)
{
	if(m_parent)
	{
		if(!m_parent->IsShown())
			m_parent->Show();
		m_parent->Raise();
		Close();
	}
}

void BeltFrame::OnPanel1EraseBackground(wxEraseEvent& event)
{
	if(!m_bgBitmap.IsOk())
        return;
	wxMemoryDC memDC;
	memDC.SelectObject(m_bgBitmap);
    event.GetDC()->StretchBlit(0,0,Panel1->GetSize().GetWidth(),Panel1->GetSize().GetHeight(),&memDC,
							0,0,m_bgBitmap.GetWidth(),m_bgBitmap.GetHeight(),wxCOPY,false);
	memDC.SelectObject(wxNullBitmap);
}

void BeltFrame::OnPanel1Resize(wxSizeEvent& event)
{
	wxSize newSize = event.GetSize();
	if(newSize.GetWidth() == m_size.GetWidth() && newSize.GetHeight() == m_size.GetHeight())
		return;
	float ratioX = newSize.GetWidth() * 1.0 / m_size.GetWidth();
	float ratioY = newSize.GetHeight() * 1.0 / m_size.GetHeight();
	m_size = newSize;
	m_curve_size.SetWidth(m_curve_size.GetWidth()*ratioX);
	m_curve_size.SetHeight(m_curve_size.GetHeight()*ratioY);

	wxPoint pt = m_title->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_title->SetPosition(pt);
	wxSize sz = m_title->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_title->SetSize(sz);

	pt = m_name->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_name->SetPosition(pt);
	sz = m_name->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_name->SetSize(sz);

	pt = m_txt_flawMax5->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_txt_flawMax5->SetPosition(pt);
	sz = m_txt_flawMax5->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_txt_flawMax5->SetSize(sz);

	pt = m_txt_flawLev->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_txt_flawLev->SetPosition(pt);
	sz = m_txt_flawLev->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_txt_flawLev->SetSize(sz);

	if(m_txt_jointMax5)
	{
		pt = m_txt_jointMax5->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_txt_jointMax5->SetPosition(pt);
		sz = m_txt_jointMax5->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_txt_jointMax5->SetSize(sz);
	}

	if(m_txt_tear)
	{
		pt = m_txt_tear->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_txt_tear->SetPosition(pt);
		sz = m_txt_tear->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_txt_tear->SetSize(sz);
	}

	if(m_txt_vis)
	{
		pt = m_txt_vis->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_txt_vis->SetPosition(pt);
		sz = m_txt_vis->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_txt_vis->SetSize(sz);
	}

	if(m_txt_run)
	{
		pt = m_txt_run->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_txt_run->SetPosition(pt);
		sz = m_txt_run->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_txt_run->SetSize(sz);
	}

	pt = m_tsb_flaw->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_tsb_flaw->SetPosition(pt);
	sz = m_tsb_flaw->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_tsb_flaw->SetSize(sz);

	if(m_tsb_joint)
	{
		pt = m_tsb_joint->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_tsb_joint->SetPosition(pt);
		sz = m_tsb_joint->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_tsb_joint->SetSize(sz);
	}

	if(m_tsb_tear)
	{
		pt = m_tsb_tear->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_tsb_tear->SetPosition(pt);
		sz = m_tsb_tear->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_tsb_tear->SetSize(sz);
	}

	pt = m_chart_flawMax5->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_chart_flawMax5->SetPosition(pt);
	sz = m_chart_flawMax5->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_chart_flawMax5->SetSize(sz);

	pt = m_chart_flawLev->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_chart_flawLev->SetPosition(pt);
	sz = m_chart_flawLev->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_chart_flawLev->SetSize(sz);

	if(m_chart_jointMax5)
	{
		pt = m_chart_jointMax5->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_chart_jointMax5->SetPosition(pt);
		sz = m_chart_jointMax5->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_chart_jointMax5->SetSize(sz);
	}

	if(m_chart_tear)
	{
		pt = m_chart_tear->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_chart_tear->SetPosition(pt);
		sz = m_chart_tear->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_chart_tear->SetSize(sz);
	}

	if(m_chart_vis)
	{
		pt = m_chart_vis->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_chart_vis->SetPosition(pt);
		sz = m_chart_vis->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_chart_vis->SetSize(sz);
	}

	if(m_view_run)
	{
		pt = m_view_run->GetPosition();
		pt.x *= ratioX;
		pt.y *= ratioY;
		m_view_run->SetPosition(pt);
		sz = m_view_run->GetSize();
		sz.SetWidth(sz.GetWidth()*ratioX);
		sz.SetHeight(sz.GetHeight()*ratioY);
		m_view_run->SetSize(sz);
	}

	m_view_selected = false;
	TcpClientView::DataEvtSelected evtSel;
	memset(&evtSel, 0, sizeof(TcpClientView::DataEvtSelected));
	strncpy(evtSel.sn, m_stw->sn, 64);
	strncpy(evtSel.node, m_stw->node, 16);
	evtSel.isRunningDt = 0x0E;
	if(m_stw->st_type == 5 && g_curve_display_mode > 0)
		evtSel.isMRTrtdt = 2;
	else
		evtSel.isMRTrtdt = 1;
	if(m_stw->st_type == 5)
		evtSel.isVICrtdt = 0x02;
	else
		evtSel.isVICrtdt = 0x01;
	evtSel.isArrange = 1;
	evtSel.screenLength = g_mrt_onescreen_len;
	if(m_stw->st_type != 5 && m_stw->rope_info.count > 1)
		evtSel.pixel_w = m_curve_size.GetWidth() - 30 ;
	else
		evtSel.pixel_w = m_curve_size.GetWidth() ;
	evtSel.pixel_h = m_curve_size.GetHeight() - 22 - 8;
	evtSel.stm_frame = g_vis_frame_rate;
	evtSel.stm_scale = 2;
	m_tcp_view->SendEvent(TcpClientView::Evt_Selected, &evtSel);

	pt = m_curve->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_curve->SetPosition(pt);
	sz = m_curve->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_curve->SetSize(sz);

	pt = m_txt_state->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_txt_state->SetPosition(pt);
	sz = m_txt_state->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_txt_state->SetSize(sz);

	pt = m_txt_pos->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_txt_pos->SetPosition(pt);
	sz = m_txt_pos->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_txt_pos->SetSize(sz);

	pt = m_txt_speed->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_txt_speed->SetPosition(pt);
	sz = m_txt_speed->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_txt_speed->SetSize(sz);

	pt = m_tsb_disconnect->GetPosition();
	pt.x *= ratioX;
	pt.y *= ratioY;
	m_tsb_disconnect->SetPosition(pt);
	sz = m_tsb_disconnect->GetSize();
	sz.SetWidth(sz.GetWidth()*ratioX);
	sz.SetHeight(sz.GetHeight()*ratioY);
	m_tsb_disconnect->SetSize(sz);

	Panel1->Refresh();
}

///更新运行数据
void BeltFrame::UpdateRunData()
{
	wxString strText;
	if((m_stw->reg_state & 0x01) == 0)
	{
		strText = _("状态：离线");
	}
	else if(m_stw->run_state & 0x01)
	{
		strText = _("状态：停止检测");
	}
	else if(m_stw->st_type == 5)
	{
		if(m_stw->run_data.jt_num[0])
		{
			strText = wxString::Format(_("找到接头：%d/%d  [%s]"), m_stw->run_data.joint, m_stw->mrt_param.jt_cnt, m_stw->run_data.jt_num);
		}
		else
		{
			strText = _("正在寻找特征接头");
		}
	}
	else
	{
		strText = _("状态：检测中");
	}
	m_txt_state->SetLabelText(strText);
	strText = wxString::Format(_("位置：%.0fm"), m_stw->run_data.pos);
	m_txt_pos->SetLabelText(strText);
	strText = wxString::Format(_("速度：%.1fm/s"), m_stw->run_data.speed);
	m_txt_speed->SetLabelText(strText);
	int appoint = 0;
	if(m_stw->reg_state & 0x01)
	{
		if(m_stw->run_state & 0x06)
			appoint = 4;
		else if(m_stw->run_state & 0x20)
			appoint = 3;
		else if(m_stw->run_state & 0x10)
			appoint = 2;
		else
			appoint = 1;
	}
	else
		appoint = 5;
	m_tsb_flaw->SetAppoint(appoint);
	if(m_stw->st_type == 5)
	{
		if(m_stw->reg_state & 0x01)
		{
			if(m_stw->run_state & 0x06)
				appoint = 4;
			else if(m_stw->run_state & 0x80)
				appoint = 3;
			else if(m_stw->run_state & 0x40)
				appoint = 2;
			else
				appoint = 1;
		}
		else
			appoint = 5;
		m_tsb_joint->SetAppoint(appoint);
		if(m_stw->reg_state & 0x04)
		{
			if(m_stw->run_state & 0x800)
				appoint = 4;
			else if(m_stw->run_state & 0x200)
				appoint = 3;
			else if(m_stw->run_state & 0x100)
				appoint = 2;
			else
				appoint = 1;
		}
		else
			appoint = 5;
		m_tsb_tear->SetAppoint(appoint);
	}
	else
	{
		m_view_run->SetCntDayTotal(m_stw->run_data.cnt_daily, m_stw->run_data.cnt_total);
		if(m_stw->run_data.shpos > 0)
			m_view_run->SetSpreaderPos(m_stw->run_data.shpos, m_stw->run_data.tppos);
		else if(m_stw->run_data.tppos > 0)
			m_view_run->SetTrolleyPos(m_stw->run_data.tppos);
		else if(m_stw->run_data.gapos > 0)
			m_view_run->SetGirderPos(m_stw->run_data.gapos);
		else if(m_stw->run_data.trip_cnt > 0)
			m_view_run->SetTripPos(m_stw->run_data.trip, m_stw->run_data.trip_cnt, m_stw->run_data.speed);
		else
			m_view_run->SetPos(m_stw->run_data.pos, m_stw->rope_info.len, m_stw->run_data.speed);
	}
}

///1秒Timer，供给TCP的HeartBeat用
void BeltFrame::OnTimer1Trigger(wxTimerEvent& event)
{
	time_t t = time(0);
	if(m_tcp_view && m_tcp_view->Heartbeat(t))
	{
		if(!m_view_selected)
		{
			TcpClientView::DataEvtSelected evtSel;
			memset(&evtSel, 0, sizeof(TcpClientView::DataEvtSelected));
			strncpy(evtSel.sn, m_stw->sn, 64);
			strncpy(evtSel.node, m_stw->node, 16);
			evtSel.isRunningDt = 0x0E;
			if(m_stw->st_type == 5 && g_curve_display_mode > 0)
				evtSel.isMRTrtdt = 2;
			else
				evtSel.isMRTrtdt = 1;
			if(m_stw->st_type == 5)
				evtSel.isVICrtdt = 0x02;
			else
				evtSel.isVICrtdt = 0x01;
			evtSel.isArrange = 1;
			evtSel.screenLength = g_mrt_onescreen_len;
			if(m_stw->st_type != 5 && m_stw->rope_info.count > 1)
				evtSel.pixel_w = m_curve_size.GetWidth() - 30 ;
			else
				evtSel.pixel_w = m_curve_size.GetWidth() ;
			evtSel.pixel_h = m_curve_size.GetHeight() - 22 - 8;
			evtSel.stm_frame = g_vis_frame_rate;
			evtSel.stm_scale = 2;
			m_tcp_view->SendEvent(TcpClientView::Evt_Selected, &evtSel);
		}
	}
}

///View接口连接，断开通知
void BeltFrame::OnViewDisconnect(wxCommandEvent& event)
{
	m_view_selected = false;
}

///启动实时流的返回结果
void BeltFrame::OnSelectedReply(wxCommandEvent& event)
{
	int reply = event.GetInt();
	if(reply & 0x01)
	{
		//wxMessageBox(wxString::Format(_("实时流启动失败！ [%d]"), reply), _("提示"));
		return;
	}
	m_view_selected = true;
}

///MRT实时流
void BeltFrame::OnMRTFlow(wxCommandEvent& event)
{
	if(!m_view_selected)
		return;

	//wxString data = event.GetString();
	//MRTRealtimeQueue_push(data, m_curve);

	static char buf[MAX_TCP_RECV_LEN];
	int channel, points;
	memset(buf, 0, MAX_TCP_RECV_LEN);
	Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	memcpy(&channel, &buf[64+16], sizeof(int));
	memcpy(&points, &buf[64+16+sizeof(int)], sizeof(int));
	m_curve->SetFlowData(channel, points, (int *)&buf[64+16+2*sizeof(int)]);

	if(g_is_save_mrt_flow && g_curve_display_mode == 0)
	{
		if(m_save_mrt_flow_fd == NULL)
		{
			m_save_mrt_flow_fd = fopen(wxString::Format("mrt_flow_pts-chan%d.dat", channel).ToStdString().c_str(), "wb");
			m_save_mrt_flow_chan = channel;
		}
		if(m_save_mrt_flow_fd)
		{
			int *pdt = (int *)&buf[64+16+2*sizeof(int)];
			for(int i=0; i<points; i++)
			{
				for(int j=0; j<channel; j++)
					fwrite(&pdt[(j*points+i)*2], sizeof(int), 2, m_save_mrt_flow_fd);
			}
		}
	}
}

#include <wx/mstream.h>
///VIS实时流
void BeltFrame::OnVICFlow(wxCommandEvent& event)
{
	static char buf[MAX_TCP_RECV_LEN];
	memset(buf, 0, MAX_TCP_RECV_LEN);
	int len = Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	typedef struct
	{
		char sn[64];			//检测点（电梯）序列号
		char node[16];		//节点号
		char r_idx;			//绳编号（单绳独拍时）
		char c_idx;			//相机序号（单节点接入多相机时）
		char isTrigger;		//是否触发帧
		char format;			//图片格式（0=RGB, 1=jpg, 2=png, 3=bmp）
		int photo_w;			//图片像素高度
		int photo_h;			//图片像素宽度
		int photo_dtlen;	//图片数据长度
		char photo_data[];	//图片二进制数据
	} PACKED VisPhotoHeader;
	VisPhotoHeader *pheader = (VisPhotoHeader *)buf;
	if(len != sizeof(VisPhotoHeader) + pheader->photo_dtlen)
	{
		Debug(0, " ~~~ Waring ~~~ VisFlow --- Length %d != %d + %d !", len, sizeof(VisPhotoHeader), pheader->photo_dtlen);
		return;
	}
	if(strcmp(m_stw->sn, pheader->sn) != 0 || strcmp(m_stw->node, pheader->node) != 0)
	{
		Debug(0, " ~~~ Waring ~~~ VisFlow --- sn[%s | %s] or node[%s | %s] do not match !", m_stw->sn, pheader->sn, m_stw->node, pheader->node);
		return;
	}
	if(pheader->format == 1) //jpg
	{
		wxMemoryInputStream mis(pheader->photo_data, pheader->photo_dtlen);
		wxImage img(mis, wxBITMAP_TYPE_JPEG);
		if(img.IsOk())
		{
			if(m_chart_vis)
				m_chart_vis->SetTearFlow(img, pheader->c_idx);
		}
		else
		{
			Debug(0, " ~~~ Waring ~~~ VisFlow --- Photo JPG format error !");
		}
	}
	else
	{
		Debug(0, " ~~~ Waring ~~~ VisFlow --- Photo format is not JPG, Not Supported !");
	}
}

///纵撕实时流
void BeltFrame::OnTearFlow(wxCommandEvent& event)
{
	static char buf[MAX_TCP_RECV_LEN];
	memset(buf, 0, MAX_TCP_RECV_LEN);
	int len = Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	typedef struct
	{
		char sn[64];			//检测点（电梯）序列号
		char node[16];		//节点号
		char c_idx;			//相机序号
		char isTrigger;		//是否纵撕触发帧
		char format;			//图片格式（0=RGB, 1=jpg, 2=png, 3=bmp）
		int photo_w;			//图片像素高度
		int photo_h;			//图片像素宽度
		int photo_dtlen;	//图片数据长度
		char photo_data[];	//图片二进制数据
	} PACKED TearPhotoHeader;
	TearPhotoHeader *pheader = (TearPhotoHeader *)buf;
	if(len != sizeof(TearPhotoHeader) + pheader->photo_dtlen)
	{
		Debug(0, " ~~~ Waring ~~~ TearFlow --- Length %d != %d + %d !", len, sizeof(TearPhotoHeader), pheader->photo_dtlen);
		return;
	}
	if(strcmp(m_stw->sn, pheader->sn) != 0 || strcmp(m_stw->node, pheader->node) != 0)
	{
		Debug(0, " ~~~ Waring ~~~ TearFlow --- sn[%s | %s] or node[%s | %s] do not match !", m_stw->sn, pheader->sn, m_stw->node, pheader->node);
		return;
	}
	if(pheader->format == 1) //jpg
	{
		wxMemoryInputStream mis(pheader->photo_data, pheader->photo_dtlen);
		wxImage img(mis, wxBITMAP_TYPE_JPEG);
		if(img.IsOk())
		{
			if(m_chart_tear)
				m_chart_tear->SetTearFlow(img, pheader->c_idx);
		}
		else
		{
			Debug(0, " ~~~ Waring ~~~ TearFlow --- Photo JPG format error !");
		}
	}
	else
	{
		Debug(0, " ~~~ Waring ~~~ TearFlow --- Photo format is not JPG, Not Supported !");
	}
}

///View接口的通知消息 --- 找到接头/抱锁器
void BeltFrame::OnFindJtOrBsq(wxCommandEvent& event)
{
	char buf[4096];
	memset(buf, 0, 4096);
	Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	if(strcmp(m_stw->sn, buf) != 0 || strcmp(m_stw->node, &buf[64]) != 0)
	{
		Debug(0, " ~~~ Waring ~~~ MRT FindJTorBsq --- sn[%s | %s] or node[%s | %s] do not match !", m_stw->sn, buf, m_stw->node, &buf[64]);
		return;
	}
	bool is_findTZJT, is_findJT;
	int jt_count, startPixel, endPixel;
	char *jt_num, *p = &buf[64+16];
	float startPos, endPos, realLen;
	memcpy(&is_findTZJT, p, sizeof(bool));
	p += sizeof(bool);
	memcpy(&is_findJT, p, sizeof(bool));
	p += sizeof(bool);
	memcpy(&jt_count, p, sizeof(int));
	p += sizeof(int);
	jt_num = p;
	p += 8;
	memcpy(&startPos, p, sizeof(float));
	p += sizeof(float);
	memcpy(&endPos, p, sizeof(float));
	p += sizeof(float);
	memcpy(&realLen, p ,sizeof(float)); // 接头真实长度
	p += sizeof(float) * 2;
	memcpy(&startPixel, p, sizeof(int));
	p += sizeof(int);
	memcpy(&endPixel, p, sizeof(int));
	p += sizeof(int);
	p += sizeof(int) * 2;
	char lv = *p++;
	if(g_dbg_main)
		Debug(0, "~~~ Find Jt or Bsq ~~~ isTZJT=%d, isJT=%d, jtnum=[%s], jtcnt=%d, startPos=%.3f, endPos=%.3f, realLen=%.3f,  lv=%d,  startPx=%d, endPx=%d",
							is_findTZJT, is_findJT, jt_num, jt_count, startPos, endPos, realLen, lv, startPixel, endPixel);
	m_curve->FindJoint(jt_num, startPixel, endPixel, lv);

	// 触发接头自动拍照 - 检查摄像头初始化状态
	if (m_jointCaptureController && is_findJT && jt_num[0] != '\0')
	{
		if (m_cameraInitState == CAMERA_INIT_SUCCESS &&
			m_jointCaptureController->IsAutoCaptureEnabled())
		{
			JointInfo jointInfo;
			jointInfo.jointNumber = wxString(jt_num, wxConvLocal);
			jointInfo.startPos = startPos;
			jointInfo.endPos = endPos;
			jointInfo.realLen = realLen;
			jointInfo.beltSpeed = m_stw->run_data.speed;
			jointInfo.level = lv;
			jointInfo.detectTime = wxDateTime::Now();

			m_jointCaptureController->OnJointDetected(jointInfo);
		}
		else if (m_cameraInitState == CAMERA_INITIALIZING)
		{
			LogMessage("摄像头正在初始化中，跳过本次拍照");
		}
		else if (m_cameraInitState == CAMERA_INIT_FAILED)
		{
			LogWarning("摄像头初始化失败，无法执行拍照");
		}
	}
}
///View接口的通知消息 --- 五处最大损伤更新
void BeltFrame::OnFlawMax5(wxCommandEvent& event)
{
	char buf[4096];
	memset(buf, 0, 4096);
	Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	if(strcmp(m_stw->sn, buf) != 0 || strcmp(m_stw->node, &buf[64]) != 0)
	{
		Debug(0, " ~~~ Waring ~~~ MRT FlawMax5 --- sn[%s | %s] or node[%s | %s] do not match !", m_stw->sn, buf, m_stw->node, &buf[64]);
		return;
	}
	float maxY = m_stw->mrt_param.upLimit;
	FlawMax5 *flawMX5 = (FlawMax5 *)&buf[64+16];
	for(int i=0; i<5; i++)
	{
		if(flawMX5[i].value > maxY)
			maxY = flawMX5[i].value;
	}
	m_chart_flawMax5->SetFlawMax5(maxY, flawMX5);

	if(g_curve_display_mode > 0)
	{
		TcpClientWeb::DataEvtGetFlawDaily dtEvt;
		dtEvt.sn = m_stw->sn;
		dtEvt.node = m_stw->node;
		dtEvt.rope = -1;
		dtEvt.value = 0.5;
		dtEvt.level = 0;
		dtEvt.type = -1;
		dtEvt.cnt = 0;
		dtEvt.page = -1;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetFlaw, &dtEvt);
	}
}
///View接口的通知消息 --- 五处最大接头更新
void BeltFrame::OnJTMax5(wxCommandEvent& event)
{
	char buf[4096];
	memset(buf, 0, 4096);
	Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	if(strcmp(m_stw->sn, buf) != 0 || strcmp(m_stw->node, &buf[64]) != 0)
	{
		Debug(0, " ~~~ Waring ~~~ MRT JTMax5 --- sn[%s | %s] or node[%s | %s] do not match !", m_stw->sn, buf, m_stw->node, &buf[64]);
		return;
	}
	if(m_chart_jointMax5)
	{
		float maxY = 50;
		JointMax5 *jtMX5 = (JointMax5 *)&buf[64+16];
		for(int i=0; i<5; i++)
		{
			if(abs(jtMX5[i].shifting) > maxY)
				maxY = abs(jtMX5[i].shifting);
		}
		m_chart_jointMax5->SetJointMax5(maxY, jtMX5);
	}
}
///View接口的通知消息 --- 六级损伤数量更新
void BeltFrame::OnFlawLev6(wxCommandEvent& event)
{
	char buf[4096];
	memset(buf, 0, 4096);
	Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	if(strcmp(m_stw->sn, buf) != 0 || strcmp(m_stw->node, &buf[64]) != 0)
	{
		Debug(0, " ~~~ Waring ~~~ MRT FlawLev6 --- sn[%s | %s] or node[%s | %s] do not match !", m_stw->sn, buf, m_stw->node, &buf[64]);
		return;
	}
	m_chart_flawLev->SetFlawLev6(30, (int *)&buf[64+16]);
}
///View接口的通知消息 --- 检测里程及次数更新
void BeltFrame::OnDetectMileage(wxCommandEvent& event)
{
	char buf[4096];
	memset(buf, 0, 4096);
	Base64_Decode((unsigned char *)event.GetString().ToStdString().c_str(), event.GetInt(), buf);
	if(strcmp(m_stw->sn, buf) != 0 || strcmp(m_stw->node, &buf[64]) != 0)
	{
		Debug(0, " ~~~ Waring ~~~ MRT DetectMileage --- sn[%s | %s] or node[%s | %s] do not match !", m_stw->sn, buf, m_stw->node, &buf[64]);
		return;
	}
	if(m_view_run)
		m_view_run->SetMileageDayTotal(((float *)&buf[64+16])[0], ((float *)&buf[64+16])[1]);

	// 更新圈数管理器
	if (m_circleManager)
	{
		float totalMileage = ((float *)&buf[64+16])[1]; // 总检测里程
		bool circleChanged = m_circleManager->UpdateMileage(totalMileage);

		if (circleChanged)
		{
			// 圈数发生变化，更新拍照控制器的圈数
			if (m_jointCaptureController)
			{
				int newCircle = m_circleManager->GetCurrentCircle();
				m_jointCaptureController->SetCurrentCircle(newCircle);
				LogMessage(wxString::Format("检测到圈数变化，更新拍照控制器圈数: %d", newCircle).ToStdString());
			}
		}
	}
}

///来自MainFrame的Web连接断开
void BeltFrame::WebDisconnected(int flag)
{
	m_tsb_disconnect->Show();
}
///来自MainFrame的Web连接重连
void BeltFrame::WebReconnected()
{
	m_tsb_disconnect->Hide();
}

///左键双击“五处最大伤”图，打开损伤数据查询界面
void BeltFrame::OnFlawDClick(wxMouseEvent& event)
{
	if(m_stw->st_type == 5)
	{
		BeltQueryFlaw dlg(this, (m_stw->reg_state & 0x01), m_stw->sn, m_stw->node, m_stw->mrt_param.sample_step, m_stw->rope_info.count, m_stw->mrt_param.sensor_cnt);
		m_query_flaw_dlg = &dlg;
		TcpClientWeb::DataEvtGetJT dtEvt;
		dtEvt.sn = m_stw->sn;
		dtEvt.node = m_stw->node;
		dtEvt.cnt = 0;
		dtEvt.page = -1;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetJT, &dtEvt);
		dlg.ShowModal();
		m_query_flaw_dlg = NULL;
	}
	else
	{
		ZXQueryFlaw dlg(this, (m_stw->reg_state & 0x01), m_stw->sn, m_stw->node);
		m_query_flaw_dlg_zx = &dlg;
		TcpClientWeb::DataEvtGetFlawDaily dtEvt;
		dtEvt.sn = m_stw->sn;
		dtEvt.node = m_stw->node;
		dtEvt.date.clear();
		dtEvt.rope = -1;
		dtEvt.value = 0;
		dtEvt.level = 0;
		dtEvt.type = -1;
		dtEvt.cnt = 0;
		dtEvt.page = 0;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetFlawDaily, &dtEvt);
		dlg.ShowModal();
		m_query_flaw_dlg_zx = NULL;
	}
}

#include "Validity.h"
static Validity *g_validity_dlg = NULL;

void BeltFrame::QueryData(int evt, Redis& jdata)
{
	if(evt == TcpClientWeb::Evt_GetFlaw)
	{
		m_curve->SetFlawData(jdata, m_stw->mrt_param.sensor_cnt * 50);	///全垂直传感器，每传感器覆盖50mm
	}
	else if(evt == TcpClientWeb::Evt_GetFlawDaily)
	{
		if(m_query_flaw_dlg)
			m_query_flaw_dlg->SetData(jdata, m_stw->mrt_param.sensor_cnt * 50);	///全垂直传感器，每传感器覆盖50mm
		else if(m_report_dlg)
			m_report_dlg->SetData(jdata);
		else if(m_query_flaw_dlg_zx)
			m_query_flaw_dlg_zx->SetData(jdata);
		else if(m_report_dlg_zx)
			m_report_dlg_zx->SetData(jdata);
	}
	else if(evt == TcpClientWeb::Evt_GetFlawAlarmLog)
	{
		if(m_query_flaw_dlg)
			m_query_flaw_dlg->SetAlarmLog(jdata);
		else if(m_query_flaw_dlg_zx)
			m_query_flaw_dlg_zx->SetAlarmLog(jdata);
	}
	else if(evt == TcpClientWeb::Evt_GetJT)
	{
		if(m_query_flaw_dlg)
			m_query_flaw_dlg->SetJtData(jdata);
		else if(m_query_joint_dlg)
			m_query_joint_dlg->SetJtData(jdata);
		else if(m_report_dlg)
			m_report_dlg->SetJtData(jdata);
	}
	else if(evt == TcpClientWeb::Evt_GetJTYBData)
	{
		if(m_query_joint_dlg)
			m_query_joint_dlg->SetJtYB(jdata);
		else if(m_report_dlg)
			m_report_dlg->SetJtYB(jdata);
	}
	else if(evt == TcpClientWeb::Evt_GetAlarmLog)
	{
		if(m_query_alarm_dlg)
			m_query_alarm_dlg->SetData(jdata);
		else if(m_query_alarm_dlg_zx)
			m_query_alarm_dlg_zx->SetData(jdata);
	}
	else if(evt == TcpClientWeb::Evt_GetTearLog)
	{
		if(m_query_tear_dlg)
			m_query_tear_dlg->SetData(jdata);
	}
	else if(evt == TcpClientWeb::Evt_GetTearAbn)
	{
		if(m_query_tear_abn_dlg)
			m_query_tear_abn_dlg->SetData(jdata);
	}
	else if(evt == TcpClientWeb::Evt_ActivityValidity || evt == TcpClientWeb::Evt_ValidityExpired) ///有效期
	{
		long long sys_t = 0, valid_t = 0;
		jdata["time"](sys_t);	///time是主板时间
		jdata["valid_t"](valid_t);
		char cd_key[512];
		memset(cd_key, 0, 512);
		jdata["cd_key"](cd_key, 511);
		if(g_validity_dlg)
		{
			g_validity_dlg->SetOverDays((sys_t - (valid_t - 7 * 86400)) / 86400);
			if(cd_key[0])
				g_validity_dlg->SetCDKey(cd_key);
		}
		else if(sys_t >= valid_t - 7 * 86400) ///提前7天弹出提示
		{
			Validity vdlg(this, m_stw->sn, m_stw->node, (sys_t - (valid_t - 7 * 86400)) / 86400);
			g_validity_dlg = &vdlg;
			if(cd_key[0])
				g_validity_dlg->SetCDKey(cd_key);
			vdlg.ShowModal();
			g_validity_dlg = NULL;
		}
	}
	else if(evt == TcpClientWeb::Evt_GetStatistics)
	{
		if(m_stat_dlg)
			m_stat_dlg->SetData(jdata);
	}
}

///左键双击“五处最大抽动”图，打开接头数据查询界面
void BeltFrame::OnJointDClick(wxMouseEvent& event)
{
	BeltQueryJoint dlg(this, m_stw->sn, m_stw->node);
	m_query_joint_dlg = &dlg;
	TcpClientWeb::DataEvtGetJT dtEvt;
	dtEvt.sn = m_stw->sn;
	dtEvt.node = m_stw->node;
	dtEvt.cnt = 0;
	dtEvt.page = -1;
	g_tcp_web->SendEvent(TcpClientWeb::Evt_GetJT, &dtEvt);
	dlg.ShowModal();
	m_query_joint_dlg = NULL;
}

void BeltFrame::QueryData_ID(int evt, Redis& jdata)
{
	if(evt == TcpClientWeb::Evt_GetJTDaily || evt == TcpClientWeb::Evt_GetJTDaily2)
	{
		if(m_query_joint_dlg)
			m_query_joint_dlg->SetJtDaily(jdata);
		else if(m_report_dlg)
			m_report_dlg->SetJtDaily(jdata);
	}
}

///左键双击报警图标，打开报警记录查询界面
void BeltFrame::OnAlarmDClick(wxMouseEvent& event)
{
	if(m_stw->st_type == 5)
	{
		BeltQueryAlarm dlg(this, m_stw->sn, m_stw->node);
		m_query_alarm_dlg = &dlg;
		TcpClientWeb::DataEvtGetFlawAlarmLog dtEvt;
		dtEvt.sn = m_stw->sn;
		dtEvt.node = m_stw->node;
		dtEvt.alarm = 0;
		dtEvt.cnt = 1000;	///最大1000条记录
		dtEvt.page = 0;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetAlarmLog, &dtEvt);
		dtEvt.alarm = 1;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetAlarmLog, &dtEvt);
		dtEvt.alarm = 2;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetAlarmLog, &dtEvt);
		dlg.ShowModal();
		m_query_alarm_dlg = NULL;
	}
	else
	{
		ZXQueryAlarm dlg(this, m_stw->sn, m_stw->node);
		m_query_alarm_dlg_zx = &dlg;
		TcpClientWeb::DataEvtGetFlawAlarmLog dtEvt;
		dtEvt.sn = m_stw->sn;
		dtEvt.node = m_stw->node;
		dtEvt.alarm = 0;
		dtEvt.cnt = 1000;	///最大1000条记录
		dtEvt.page = 0;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetAlarmLog, &dtEvt);
		dtEvt.alarm = 1;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetAlarmLog, &dtEvt);
		dlg.ShowModal();
		m_query_alarm_dlg_zx = NULL;
	}
}

void BeltFrame::OperateReply(int evt, int reply)
{
	if(evt == TcpClientWeb::Evt_FlawMarkAsKnown)
	{
		if(m_query_flaw_dlg)
			m_query_flaw_dlg->FlawMarkReply(reply);
		else if(m_query_flaw_dlg_zx)
			m_query_flaw_dlg_zx->FlawMarkReply(reply);
	}
	else if(evt == TcpClientWeb::Evt_JointYBRedo)
	{
		if(m_query_joint_dlg)
			m_query_joint_dlg->YBRedoReply(reply);
	}
	else if(evt == TcpClientWeb::Evt_AlarmReset)
	{
		if(m_query_alarm_dlg)
			m_query_alarm_dlg->ResetReply(reply);
		else if(m_query_alarm_dlg_zx)
			m_query_alarm_dlg_zx->ResetReply(reply);
	}
}

///左键双击“损伤分级统计”图，打开打印报告界面
void BeltFrame::OnReportDClick(wxMouseEvent& event)
{
	if(m_stw->st_type == 5)
	{
		BeltReport dlg(this, m_stw->sn, m_stw->node, GetLabel().ToStdString().c_str(), &m_stw->rope_info, m_stw->mrt_param.sensor_cnt, m_stw->mrt_param.upLimit);
		m_report_dlg = &dlg;
		TcpClientWeb::DataEvtGetFlawDaily dtEvt;
		dtEvt.sn = m_stw->sn;
		dtEvt.node = m_stw->node;
		dtEvt.date.clear();
		dtEvt.rope = -1;
		dtEvt.value = 0;
		dtEvt.level = 0;
		dtEvt.type = -1;
		dtEvt.cnt = 0;
		dtEvt.page = 0;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetFlawDaily, &dtEvt);
		dlg.ShowModal();
		m_report_dlg = NULL;
	}
	else
	{
		ZXReport dlg(this, m_stw->sn, m_stw->node, GetLabel().ToStdString().c_str(), &m_stw->rope_info, m_stw->mrt_param.sensor_cnt, m_stw->mrt_param.upLimit);
		m_report_dlg_zx = &dlg;
		TcpClientWeb::DataEvtGetFlawDaily dtEvt;
		dtEvt.sn = m_stw->sn;
		dtEvt.node = m_stw->node;
		dtEvt.date.clear();
		dtEvt.rope = -1;
		dtEvt.value = 0;
		dtEvt.level = 0;
		dtEvt.type = -1;
		dtEvt.cnt = 0;
		dtEvt.page = 0;
		g_tcp_web->SendEvent(TcpClientWeb::Evt_GetFlawDaily, &dtEvt);
		dlg.ShowModal();
		m_report_dlg_zx = NULL;
	}
}

///左键双击“纵撕实时图像”，打开纵撕记录查询界面
void BeltFrame::OnTearDClick(wxMouseEvent& event)
{
	BeltQueryTear dlg(this, m_stw->sn, m_stw->node, &m_stw->rope_info);
	m_query_tear_dlg = &dlg;
	TcpClientWeb::DataEvtGetFlawAlarmLog dtEvt;
	dtEvt.sn = m_stw->sn;
	dtEvt.node = m_stw->node;
	///默认查询1年内的
	dtEvt.sdate = wxDateTime::Now().Subtract(wxDateSpan(1, 0, 0, 0)).FormatISOCombined(' ').ToStdString();
	dtEvt.edate = wxDateTime::Now().FormatISOCombined(' ').ToStdString();
	dtEvt.cnt = 1000;	///最大1000条记录
	dtEvt.page = 0;
	g_tcp_web->SendEvent(TcpClientWeb::Evt_GetTearLog, &dtEvt);
	dlg.ShowModal();
	m_query_tear_dlg = NULL;
}

///左键双击纵撕的报警图标，打开纵撕故障记录查询界面
void BeltFrame::OnTearAbnDClick(wxMouseEvent& event)
{
	BeltQueryTearAbn dlg(this);
	m_query_tear_abn_dlg = &dlg;
	TcpClientWeb::DataEvtGetFlawAlarmLog dtEvt;
	dtEvt.sn = m_stw->sn;
	dtEvt.node = m_stw->node;
	///默认查询1年内的
	dtEvt.sdate = wxDateTime::Now().Subtract(wxDateSpan(1, 0, 0, 0)).FormatISOCombined(' ').ToStdString();
	dtEvt.edate = wxDateTime::Now().FormatISOCombined(' ').ToStdString();
	dtEvt.cnt = 1000;	///最大1000条记录
	dtEvt.page = 0;
	g_tcp_web->SendEvent(TcpClientWeb::Evt_GetTearAbn, &dtEvt);
	dlg.ShowModal();
	m_query_tear_abn_dlg = NULL;
}

///左键双击“视觉实时图像”，
void BeltFrame::OnVisDClick(wxMouseEvent& event)
{

}

///左键双击“运行动态”，打开监测统计信息窗口
void BeltFrame::OnRunningDClick(wxMouseEvent& event)
{
	ZXStatDialog dlg(this, m_stw->sn, m_stw->node, m_stw->rope_info.len);
	m_stat_dlg = &dlg;
	TcpClientWeb::DataEvtGetStatisticsLog dtEvt;
	dtEvt.sn = m_stw->sn;
	dtEvt.node = m_stw->node;
	dtEvt.type = 0;
	dtEvt.rope = 0;
	dtEvt.flaw_filter = 0;
	g_tcp_web->SendEvent(TcpClientWeb::Evt_GetStatistics, &dtEvt);
	dlg.ShowModal();
	m_stat_dlg = NULL;
}

// 异步初始化摄像头
void BeltFrame::InitializeCameraAsync()
{
	if (m_cameraInitState != CAMERA_NOT_INIT) {
		return; // 避免重复初始化
	}

	m_cameraInitState = CAMERA_INITIALIZING;
	m_cameraStatusText->SetLabelText(_("正在初始化摄像头..."));

	// 创建后台线程执行初始化
	m_initThread = new std::thread([this]() {
		bool success = false;
		wxString errorMsg;

		try {
			// 加载配置
			CaptureConfig config;
			if (!config.LoadConfig()) {
				errorMsg = _("加载配置文件失败");
			} else {
				// 初始化摄像头
				success = m_jointCaptureController->Initialize(
					config.GetCameraIP(), config.GetCameraPort(),
					config.GetCameraUsername(), config.GetCameraPassword());

				if (success) {
					m_jointCaptureController->SetCameraDistance(config.GetCameraDistance());
					m_jointCaptureController->EnableAutoCapture(config.IsAutoCaptureEnabled());
					m_jointCaptureController->GetPhotoStorageManager()->SetRootPath(config.GetPhotoRootPath());
				} else {
					errorMsg = m_jointCaptureController->GetLastError();
				}
			}
		} catch (const std::exception& e) {
			success = false;
			errorMsg = wxString::Format(_("初始化异常: %s"), e.what());
		} catch (...) {
			success = false;
			errorMsg = _("初始化发生未知异常");
		}

		// 通知主线程初始化结果
		wxCommandEvent evt(wxEVT_COMMAND_MENU_SELECTED, ID_CAMERA_INIT_COMPLETE);
		evt.SetInt(success ? 1 : 0);
		evt.SetString(errorMsg);
		wxPostEvent(this, evt);
	});
}

// 处理摄像头初始化完成事件
void BeltFrame::OnCameraInitComplete(wxCommandEvent& event)
{
	bool success = (event.GetInt() == 1);
	wxString errorMsg = event.GetString();

	if (success) {
		m_cameraInitState = CAMERA_INIT_SUCCESS;
		m_cameraStatusText->SetLabelText(_("摄像头初始化成功"));
		LogMessage("摄像头初始化成功");

		// 成功后2秒自动隐藏状态文本
		m_statusHideTimer->StartOnce(2000);
	} else {
		m_cameraInitState = CAMERA_INIT_FAILED;
		m_cameraStatusText->SetLabelText(_("摄像头初始化失败"));
		LogWarning("摄像头初始化失败: " + errorMsg.ToStdString());

		// 失败后8秒自动隐藏状态文本
		m_statusHideTimer->StartOnce(8000);
	}

	// 清理线程资源
	if (m_initThread && m_initThread->joinable()) {
		m_initThread->join();
		delete m_initThread;
		m_initThread = nullptr;
	}
}

// 处理状态文本自动隐藏定时器事件
void BeltFrame::OnStatusHideTimer(wxTimerEvent& event)
{
	// 隐藏状态文本
	if (m_cameraStatusText) {
		m_cameraStatusText->Hide();
	}
}

