{"files.associations": {"new": "cpp", "cmath": "cpp", "thread": "cpp", "array": "cpp", "atomic": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "optional": "cpp", "ratio": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp"}}