#include "PhotoStorageManager.h"
#include <wx/filename.h>
#include <wx/dir.h>
#include <wx/filefn.h>
#include <wx/regex.h>
#include "Logger.h"

PhotoStorageManager::PhotoStorageManager()
    : m_rootPath("photos")
    , m_currentCircle(1)
{
}

PhotoStorageManager::~PhotoStorageManager()
{
}

void PhotoStorageManager::SetRootPath(const wxString& rootPath)
{
    m_rootPath = rootPath;
}

wxString PhotoStorageManager::GeneratePhotoPath(const wxString& jointNumber, 
                                               const wxDateTime& date,
                                               int circle)
{
    if (circle == -1)
        circle = m_currentCircle;

    // 生成目录结构：photos/YYYY-MM-DD/circle_N/
    wxString dateStr = date.Format("%Y-%m-%d");
    wxString circleDir = wxString::Format("circle_%d", circle);
    wxString directory = wxFileName(m_rootPath, "").GetFullPath();
    directory = wxFileName(directory, dateStr).GetFullPath();
    directory = wxFileName(directory, circleDir).GetFullPath();

    // 生成文件名格式：joint_001_20240115143022.jpg
    wxString formattedJointNum = FormatJointNumber(jointNumber);
    wxString timestamp = GenerateTimestamp();
    wxString fileName = wxString::Format("joint_%s_%s.jpg", formattedJointNum, timestamp);

    return wxFileName(directory, fileName).GetFullPath();
}

bool PhotoStorageManager::CreateDirectories(const wxDateTime& date, int circle)
{
    if (circle == -1)
        circle = m_currentCircle;

    wxString directory = GetCircleDirectory(date, circle);
    
    if (!wxFileName::DirExists(directory))
    {
        return wxFileName::Mkdir(directory, wxS_DIR_DEFAULT, wxPATH_MKDIR_FULL);
    }
    
    return true;
}

wxString PhotoStorageManager::GetLatestPhotoPath(const wxString& jointNumber, 
                                                const wxDateTime& date)
{
    wxString formattedJointNum = FormatJointNumber(jointNumber);
    wxString latestPhoto;
    wxString latestTimestamp;

    // 遍历指定日期的所有圈次目录
    wxString dateStr = date.Format("%Y-%m-%d");
    wxString dayDirectory = wxFileName(m_rootPath, dateStr).GetFullPath();
    
    if (!wxFileName::DirExists(dayDirectory))
        return wxEmptyString;

    wxDir dir(dayDirectory);
    if (!dir.IsOpened())
        return wxEmptyString;

    wxString circleDir;
    bool cont = dir.GetFirst(&circleDir, "circle_*", wxDIR_DIRS);
    
    while (cont)
    {
        wxString circleFullPath = wxFileName(dayDirectory, circleDir).GetFullPath();
        wxArrayString photos = GetPhotosInDirectory(circleFullPath);

        // LogMessage("检查圈次目录: " + circleFullPath.ToStdString() + ", 找到 " +
        //           wxString::Format("%d", (int)photos.GetCount()).ToStdString() + " 张照片");
        
        // 遍历圈次目录中的所有照片
        for (size_t i = 0; i < photos.GetCount(); i++)
        {
            wxString fileName = wxFileName(photos[i]).GetFullName();
            wxString pattern = wxString::Format("joint_%s_", formattedJointNum);

            // LogMessage("检查文件: " + fileName.ToStdString() + ", 匹配模式: " + pattern.ToStdString());

            if (fileName.StartsWith(pattern))
            {
                // LogMessage("找到匹配文件: " + fileName.ToStdString());
                wxString timestamp = ExtractTimestamp(fileName);
                if (timestamp > latestTimestamp)
                {
                    latestTimestamp = timestamp;
                    latestPhoto = photos[i];
                }
            }
        }
        
        cont = dir.GetNext(&circleDir);
    }

    LogMessage("最终结果: " + (latestPhoto.IsEmpty() ? std::string("未找到") : latestPhoto.ToStdString()));
    return latestPhoto;
}

wxString PhotoStorageManager::GetCircleDirectory(const wxDateTime& date, int circle)
{
    if (circle == -1)
        circle = m_currentCircle;

    wxString dateStr = date.Format("%Y-%m-%d");
    wxString circleDir = wxString::Format("circle_%d", circle);
    
    wxString directory = wxFileName(m_rootPath, "").GetFullPath();
    directory = wxFileName(directory, dateStr).GetFullPath();
    directory = wxFileName(directory, circleDir).GetFullPath();
    
    return directory;
}

bool PhotoStorageManager::PhotoExists(const wxString& filePath)
{
    return wxFileName::FileExists(filePath);
}

wxArrayString PhotoStorageManager::GetPhotosInDirectory(const wxString& directory)
{
    wxArrayString photos;
    
    if (!wxFileName::DirExists(directory))
        return photos;

    wxDir dir(directory);
    if (!dir.IsOpened())
        return photos;

    wxString fileName;
    bool cont = dir.GetFirst(&fileName, "joint_*.jpg", wxDIR_FILES);
    
    while (cont)
    {
        photos.Add(wxFileName(directory, fileName).GetFullPath());
        cont = dir.GetNext(&fileName);
    }

    return photos;
}

void PhotoStorageManager::CleanupOldPhotos(int daysToKeep)
{
    // 清理旧照片的功能
    // 这里可以在未来版本中实现删除过期照片的逻辑
}

wxString PhotoStorageManager::FormatJointNumber(const wxString& jointNumber)
{
    // 如果接头号为空，返回默认值
    if (jointNumber.IsEmpty())
        return "000";

    // 直接使用原始接头号，但需要处理文件名不允许的字符
    wxString result = jointNumber;

    // 替换文件名中不允许的字符为下划线
    result.Replace("/", "_");
    result.Replace("\\", "_");
    result.Replace(":", "_");
    result.Replace("*", "_");
    result.Replace("?", "_");
    result.Replace("\"", "_");
    result.Replace("<", "_");
    result.Replace(">", "_");
    result.Replace("|", "_");
    result.Replace(" ", "_");  // 空格也替换为下划线

    // 限制长度不超过8个字符
    if (result.Length() > 8)
        result = result.Left(8);

    return result;
}

wxString PhotoStorageManager::GenerateTimestamp(const wxDateTime& dateTime)
{
    return dateTime.Format("%Y%m%d%H%M%S");
}

wxString PhotoStorageManager::ExtractTimestamp(const wxString& fileName)
{
    // 从文件名中提取时间戳，支持各种字符的接头号，如：joint_6#_20250723105947.jpg
    wxRegEx regex("joint_[^_]+_([0-9]{14})\\.jpg");

    if (regex.Matches(fileName))
    {
        return regex.GetMatch(fileName, 1);
    }

    return wxEmptyString;
}
