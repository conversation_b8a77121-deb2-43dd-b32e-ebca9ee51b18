# depslib dependency file v1.0
********** source:e:\tck.w_client_iot\debugwin.cpp
	"DebugWin.h"
	<wx/intl.h>
	<wx/string.h>

********** e:\tck.w_client_iot\debugwin.h
	<wx/dialog.h>
	<wx/textctrl.h>
	"yblib.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\textctrl.h
	"wx/defs.h"
	"wx/control.h"
	"wx/textentry.h"
	"wx/dynarray.h"
	"wx/gdicmn.h"
	"wx/ioswrap.h"
	"wx/x11/textctrl.h"
	"wx/univ/textctrl.h"
	"wx/msw/wince/textctrlce.h"
	"wx/msw/textctrl.h"
	"wx/motif/textctrl.h"
	"wx/gtk/textctrl.h"
	"wx/gtk1/textctrl.h"
	"wx/osx/textctrl.h"
	"wx/cocoa/textctrl.h"
	"wx/os2/textctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\defs.h
	"wx/platform.h"
	"wx/version.h"
	"wx/dlimpexp.h"
	<stddef.h>
	"wx/debug.h"
	<sys/types.h>
	<sys/types.h>
	"wx/windowid.h"
	<unistd.h>
	"wx/msw/winundef.h"
	"wx/features.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\platform.h
	<unistd.h>
	<TargetConditionals.h>
	<AvailabilityMacros.h>
	"wx/osx/config_xcode.h"
	"wx/android/config_android.h"
	"wx/compiler.h"
	"wx/setup.h"
	"wx/msw/wince/libraries.h"
	"wx/msw/libraries.h"
	"wx/msw/gccpriv.h"
	<TargetConditionals.h>
	<AvailabilityMacros.h>
	"wx/chkconf.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\config_xcode.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\android\config_android.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\compiler.h

1412609644 d:\codeblocks-17.12\wxwidgets-3.0.2\lib\gcc_dll\mswu\wx\setup.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\libraries.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\libraries.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\gccpriv.h
	<_mingw.h>
	<w32api.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\chkconf.h
	"wx/msw/wince/chkconf.h"
	"wx/msw/chkconf.h"
	"wx/gtk/chkconf.h"
	"wx/gtk/chkconf.h"
	"wx/cocoa/chkconf.h"
	"wx/osx/chkconf.h"
	"wx/os2/chkconf.h"
	"wx/dfb/chkconf.h"
	"wx/motif/chkconf.h"
	"wx/x11/chkconf.h"
	"wx/android/chkconf.h"
	"wx/unix/chkconf.h"
	"wx/univ/chkconf.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\chkconf.h
	"wx/osx/iphone/chkconf.h"
	"wx/osx/carbon/chkconf.h"
	"wx/osx/cocoa/chkconf.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\iphone\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\carbon\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\cocoa\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\android\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\unix\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\chkconf.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\version.h
	"wx/cpp.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cpp.h
	"wx/compiler.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dlimpexp.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\debug.h
	<assert.h>
	<limits.h>
	"wx/chartype.h"
	"wx/cpp.h"
	"wx/dlimpexp.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\chartype.h
	"wx/platform.h"
	<sys/types.h>
	<wchar.h>
	<wcstr.h>
	<stdlib.h>
	<widec.h>
	<ctype.h>
	<stddef.h>
	<string.h>
	<ctype.h>
	<tchar.h>
	<ctype.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\windowid.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\winundef.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\features.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\control.h
	"wx/defs.h"
	"wx/window.h"
	"wx/univ/control.h"
	"wx/msw/control.h"
	"wx/motif/control.h"
	"wx/gtk/control.h"
	"wx/gtk1/control.h"
	"wx/osx/control.h"
	"wx/cocoa/control.h"
	"wx/os2/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\window.h
	"wx/event.h"
	"wx/list.h"
	"wx/cursor.h"
	"wx/font.h"
	"wx/colour.h"
	"wx/region.h"
	"wx/utils.h"
	"wx/intl.h"
	"wx/validate.h"
	"wx/palette.h"
	"wx/accel.h"
	"wx/access.h"
	"wx/msw/window.h"
	"wx/motif/window.h"
	"wx/gtk/window.h"
	"wx/gtk1/window.h"
	"wx/x11/window.h"
	"wx/dfb/window.h"
	"wx/osx/window.h"
	"wx/cocoa/window.h"
	"wx/os2/window.h"
	"wx/univ/window.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\event.h
	"wx/defs.h"
	"wx/cpp.h"
	"wx/object.h"
	"wx/clntdata.h"
	"wx/gdicmn.h"
	"wx/cursor.h"
	"wx/mousestate.h"
	"wx/dynarray.h"
	"wx/thread.h"
	"wx/tracker.h"
	"wx/typeinfo.h"
	"wx/any.h"
	"wx/meta/convertible.h"
	"wx/meta/removeref.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\object.h
	"wx/memory.h"
	"wx/xti.h"
	"wx/rtti.h"
	"wx/xti2.h"
	"wx/msw/msvcrt.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\memory.h
	"wx/defs.h"
	"wx/string.h"
	"wx/msgout.h"
	<stddef.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\string.h
	"wx/defs.h"
	<ctype.h>
	<stdio.h>
	<string.h>
	<stdarg.h>
	<limits.h>
	<string.h>
	<stdio.h>
	<stdarg.h>
	<limits.h>
	<stdlib.h>
	"wx/wxcrtbase.h"
	"wx/strvararg.h"
	"wx/buffer.h"
	"wx/strconv.h"
	"wx/stringimpl.h"
	"wx/stringops.h"
	"wx/unichar.h"
	"wx/tls.h"
	"wx/iosfwrap.h"
	"wx/crt.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\wxcrtbase.h
	"wx/chartype.h"
	<stdio.h>
	<string.h>
	<ctype.h>
	<wctype.h>
	<time.h>
	<io.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\strvararg.h
	"wx/platform.h"
	"wx/cpp.h"
	"wx/chartype.h"
	"wx/strconv.h"
	"wx/buffer.h"
	"wx/unichar.h"
	<type_traits>
	<type_traits>
	<tr1/type_traits>
	"wx/stringimpl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\strconv.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/buffer.h"
	"typeinfo.h"
	<stdlib.h>
	"wx/fontenc.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\buffer.h
	"wx/chartype.h"
	"wx/wxcrtbase.h"
	<stdlib.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\typeinfo.h
	"wx/defs.h"
	<typeinfo>
	<string.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\fontenc.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\unichar.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/stringimpl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\stringimpl.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/wxcrtbase.h"
	<stdlib.h>
	"wx/beforestd.h"
	<string>
	"wx/afterstd.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\beforestd.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\afterstd.h
	"wx/msw/winundef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\stringops.h
	"wx/chartype.h"
	"wx/stringimpl.h"
	"wx/unichar.h"
	"wx/buffer.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\tls.h
	"wx/defs.h"
	"wx/msw/tls.h"
	"wx/os2/tls.h"
	"wx/unix/tls.h"
	<stdlib.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\tls.h
	"wx/msw/wrapwin.h"
	"wx/thread.h"
	"wx/vector.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wrapwin.h
	"wx/platform.h"
	<winsock2.h>
	<windows.h>
	"wx/msw/winundef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\thread.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\vector.h
	"wx/defs.h"
	<vector>
	<algorithm>
	"wx/scopeguard.h"
	"wx/meta/movable.h"
	"wx/meta/if.h"
	"wx/beforestd.h"
	<new>
	"wx/afterstd.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\scopeguard.h
	"wx/defs.h"
	"wx/except.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\except.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\meta\movable.h
	"wx/meta/pod.h"
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\meta\pod.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\meta\if.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\tls.h
	"wx/os2/private.h"
	"wx/thread.h"
	"wx/vector.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\private.h
	<os2.h>
	<os2def.h>
	<X11/Xmd.h>
	<Xm/VendorSP.h>
	<types.h>
	<tcpustd.h>
	<sys/time.h>
	"wx/dlimpexp.h"
	"wx/fontenc.h"
	"wx/thread.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\unix\tls.h
	<pthread.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\iosfwrap.h
	<iostream.h>
	<iosfwd>
	"wx/msw/winundef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\crt.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/wxcrt.h"
	"wx/wxcrtvararg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\wxcrt.h
	"wx/wxcrtbase.h"
	"wx/string.h"
	<string.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\wxcrtvararg.h
	"wx/wxcrt.h"
	"wx/strvararg.h"
	"wx/string.h"
	<stdarg.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msgout.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/strvararg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xti.h
	"wx/defs.h"
	"wx/xtitypes.h"
	"wx/xtihandler.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xtitypes.h
	"wx/defs.h"
	"wx/string.h"
	"wx/hashmap.h"
	"wx/arrstr.h"
	"wx/flags.h"
	"wx/intl.h"
	"wx/log.h"
	<typeinfo>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\hashmap.h
	"wx/string.h"
	"wx/wxcrt.h"
	<unordered_map>
	<tr1/unordered_map>
	<ext/hash_map>
	<hash_map>
	<stddef.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\arrstr.h
	"wx/defs.h"
	"wx/string.h"
	"wx/dynarray.h"
	"wx/beforestd.h"
	<iterator>
	"wx/afterstd.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dynarray.h
	"wx/defs.h"
	"wx/beforestd.h"
	<vector>
	<algorithm>
	"wx/afterstd.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\flags.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\intl.h
	"wx/defs.h"
	"wx/string.h"
	"wx/translation.h"
	"wx/fontenc.h"
	"wx/language.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\translation.h
	"wx/defs.h"
	"wx/string.h"
	"wx/buffer.h"
	"wx/language.h"
	"wx/hashmap.h"
	"wx/strconv.h"
	"wx/scopedptr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\language.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\scopedptr.h
	"wx/defs.h"
	"wx/checkeddelete.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\checkeddelete.h
	"wx/cpp.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\log.h
	"wx/defs.h"
	"wx/cpp.h"
	"wx/string.h"
	"wx/strvararg.h"
	"wx/arrstr.h"
	<time.h>
	"wx/dynarray.h"
	"wx/hashmap.h"
	"wx/thread.h"
	"wx/iosfwrap.h"
	"wx/generic/logg.h"
	"wx/cocoa/log.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\logg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\log.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xtihandler.h
	"wx/defs.h"
	"wx/xti.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\rtti.h
	"wx/memory.h"
	"wx/flags.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xti2.h
	"wx/xtiprop.h"
	"wx/xtictor.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xtiprop.h
	"wx/defs.h"
	"wx/xti.h"
	"wx/any.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\any.h
	"wx/defs.h"
	<new>
	"wx/string.h"
	"wx/meta/if.h"
	"wx/typeinfo.h"
	"wx/list.h"
	"wx/datetime.h"
	"wx/variant.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\list.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/vector.h"
	"wx/beforestd.h"
	<algorithm>
	<iterator>
	<list>
	"wx/afterstd.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\datetime.h
	"wx/defs.h"
	"wx/msw/wince/time.h"
	<time.h>
	<limits.h>
	"wx/longlong.h"
	"wx/anystr.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\time.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\longlong.h
	"wx/defs.h"
	"wx/string.h"
	<limits.h>
	"wx/iosfwrap.h"
	<limits>
	"wx/strvararg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\anystr.h
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\variant.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/arrstr.h"
	"wx/list.h"
	"wx/cpp.h"
	"wx/longlong.h"
	"wx/datetime.h"
	"wx/iosfwrap.h"
	"wx/any.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xtictor.h
	"wx/defs.h"
	"wx/xti.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\msvcrt.h
	<stdlib.h>
	<crtdbg.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\clntdata.h
	"wx/defs.h"
	"wx/string.h"
	"wx/hashmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gdicmn.h
	"wx/defs.h"
	"wx/list.h"
	"wx/string.h"
	"wx/fontenc.h"
	"wx/hashmap.h"
	"wx/math.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\math.h
	"wx/defs.h"
	<math.h>
	<cmath>
	<float.h>
	<ieeefp.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cursor.h
	"wx/defs.h"
	"wx/msw/cursor.h"
	"wx/motif/cursor.h"
	"wx/gtk/cursor.h"
	"wx/gtk1/cursor.h"
	"wx/x11/cursor.h"
	"wx/dfb/cursor.h"
	"wx/osx/cursor.h"
	"wx/cocoa/cursor.h"
	"wx/os2/cursor.h"
	"wx/utils.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\cursor.h
	"wx/msw/gdiimage.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\gdiimage.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gdiobj.h
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\cursor.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\cursor.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\cursor.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\image.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/gdicmn.h"
	"wx/hashmap.h"
	"wx/arrstr.h"
	"wx/stream.h"
	"wx/variant.h"
	"wx/imagbmp.h"
	"wx/imagpng.h"
	"wx/imaggif.h"
	"wx/imagpcx.h"
	"wx/imagjpeg.h"
	"wx/imagtga.h"
	"wx/imagtiff.h"
	"wx/imagpnm.h"
	"wx/imagxpm.h"
	"wx/imagiff.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\stream.h
	"wx/defs.h"
	<stdio.h>
	"wx/object.h"
	"wx/string.h"
	"wx/filefn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\filefn.h
	"wx/list.h"
	"wx/arrstr.h"
	"wx/msw/wince/time.h"
	"wx/msw/private.h"
	<time.h>
	<sys/types.h>
	<sys/stat.h>
	<process.h>
	"wx/os2/private.h"
	<direct.h>
	<io.h>
	<unistd.h>
	<unistd.h>
	<dirent.h>
	<direct.h>
	<dos.h>
	<io.h>
	<direct.h>
	<dos.h>
	<io.h>
	<io.h>
	<unistd.h>
	<dir.h>
	<fcntl.h>
	<sys/types.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\private.h
	"wx/msw/wrapwin.h"
	"wx/msw/microwin.h"
	"wx/log.h"
	"wx/window.h"
	"wx/gdicmn.h"
	"wx/colour.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\microwin.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\colour.h
	"wx/defs.h"
	"wx/gdiobj.h"
	"wx/variant.h"
	"wx/msw/colour.h"
	"wx/motif/colour.h"
	"wx/gtk/colour.h"
	"wx/gtk1/colour.h"
	"wx/generic/colour.h"
	"wx/x11/colour.h"
	"wx/osx/colour.h"
	"wx/cocoa/colour.h"
	"wx/os2/colour.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\colour.h
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\colour.h
	"wx/object.h"
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\colour.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\colour.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/gdiobj.h"
	"wx/palette.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\palette.h
	"wx/defs.h"
	"wx/object.h"
	"wx/gdiobj.h"
	"wx/msw/palette.h"
	"wx/x11/palette.h"
	"wx/generic/paletteg.h"
	"wx/osx/palette.h"
	"wx/os2/palette.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\palette.h
	"wx/gdiobj.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\palette.h
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\paletteg.h
	"wx/defs.h"
	"wx/object.h"
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\palette.h
	"wx/gdiobj.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\palette.h
	"wx/gdiobj.h"
	"wx/os2/private.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\colour.h
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\colour.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/gdiobj.h"
	"wx/palette.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\colour.h
	"wx/osx/core/colour.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\core\colour.h
	"wx/object.h"
	"wx/string.h"
	"wx/osx/core/cfref.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\core\cfref.h
	<unistd.h>
	<AvailabilityMacros.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\colour.h
	"wx/object.h"
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\colour.h
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagbmp.h
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagpng.h
	"wx/defs.h"
	"wx/image.h"
	"wx/versioninfo.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\versioninfo.h
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imaggif.h
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagpcx.h
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagjpeg.h
	"wx/defs.h"
	"wx/image.h"
	"wx/versioninfo.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagtga.h
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagtiff.h
	"wx/defs.h"
	"wx/image.h"
	"wx/versioninfo.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagpnm.h
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagxpm.h
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imagiff.h
	"wx/image.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\cursor.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/colour.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\cursor.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\cursor.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\bitmap.h
	"wx/string.h"
	"wx/gdicmn.h"
	"wx/colour.h"
	"wx/image.h"
	"wx/variant.h"
	"wx/msw/bitmap.h"
	"wx/x11/bitmap.h"
	"wx/gtk/bitmap.h"
	"wx/gtk1/bitmap.h"
	"wx/x11/bitmap.h"
	"wx/dfb/bitmap.h"
	"wx/osx/bitmap.h"
	"wx/cocoa/bitmap.h"
	"wx/os2/bitmap.h"
	"wx/generic/mask.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\bitmap.h
	"wx/msw/gdiimage.h"
	"wx/math.h"
	"wx/palette.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\bitmap.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/palette.h"
	"wx/gdiobj.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\bitmap.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\bitmap.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/palette.h"
	"wx/gdiobj.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\bitmap.h
	"wx/dfb/dfbptr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\dfbptr.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\bitmap.h
	"wx/palette.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\bitmap.h
	"wx/palette.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\bitmap.h
	"wx/os2/private.h"
	"wx/os2/gdiimage.h"
	"wx/gdicmn.h"
	"wx/palette.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\gdiimage.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\mask.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\cursor.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\cursor.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\utils.h
	"wx/object.h"
	"wx/list.h"
	"wx/filefn.h"
	"wx/hashmap.h"
	"wx/versioninfo.h"
	"wx/meta/implicitconversion.h"
	"wx/gdicmn.h"
	"wx/mousestate.h"
	"wx/longlong.h"
	"wx/platinfo.h"
	<direct.h>
	<dirent.h>
	<unistd.h>
	<stdio.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\meta\implicitconversion.h
	"wx/defs.h"
	"wx/meta/if.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\mousestate.h
	"wx/gdicmn.h"
	"wx/kbdstate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\kbdstate.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\platinfo.h
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\tracker.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\meta\convertible.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\meta\removeref.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\font.h
	"wx/defs.h"
	"wx/fontenc.h"
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/msw/font.h"
	"wx/motif/font.h"
	"wx/gtk/font.h"
	"wx/gtk1/font.h"
	"wx/x11/font.h"
	"wx/dfb/font.h"
	"wx/osx/font.h"
	"wx/cocoa/font.h"
	"wx/os2/font.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\font.h
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\font.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\font.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\font.h
	"wx/hash.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\hash.h
	"wx/defs.h"
	"wx/string.h"
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\font.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\font.h
	"wx/dfb/dfbptr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\font.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\font.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\font.h
	"wx/gdiobj.h"
	"wx/os2/private.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\region.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/msw/region.h"
	"wx/gtk/region.h"
	"wx/gtk1/region.h"
	"wx/x11/region.h"
	"wx/dfb/region.h"
	"wx/osx/region.h"
	"wx/cocoa/region.h"
	"wx/os2/region.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\region.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\region.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\region.h
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\region.h
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\region.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\region.h
	"wx/osx/carbon/region.h"
	"wx/generic/region.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\carbon\region.h
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\region.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\region.h
	"wx/generic/region.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\region.h
	"wx/list.h"
	"wx/os2/private.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\validate.h
	"wx/defs.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\accel.h
	"wx/defs.h"
	"wx/object.h"
	"wx/generic/accel.h"
	"wx/msw/accel.h"
	"wx/motif/accel.h"
	"wx/gtk/accel.h"
	"wx/gtk1/accel.h"
	"wx/osx/accel.h"
	"wx/generic/accel.h"
	"wx/os2/accel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\accel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\accel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\accel.h
	"wx/object.h"
	"wx/string.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\accel.h
	"wx/generic/accel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\accel.h
	"wx/generic/accel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\accel.h
	"wx/string.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\accel.h
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\access.h
	"wx/defs.h"
	"wx/variant.h"
	"wx/msw/ole/access.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\ole\access.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\window.h
	"wx/settings.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\settings.h
	"wx/colour.h"
	"wx/font.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\window.h
	"wx/region.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\window.h
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\window.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\window.h
	"wx/region.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\window.h
	"wx/dfb/dfbptr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\window.h
	"wx/brush.h"
	"wx/dc.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\brush.h
	"wx/defs.h"
	"wx/object.h"
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/msw/brush.h"
	"wx/x11/brush.h"
	"wx/gtk/brush.h"
	"wx/gtk1/brush.h"
	"wx/dfb/brush.h"
	"wx/osx/brush.h"
	"wx/cocoa/brush.h"
	"wx/os2/brush.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\brush.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\brush.h
	"wx/gdiobj.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\brush.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\brush.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/gdiobj.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\brush.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/gdiobj.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\brush.h
	"wx/gdicmn.h"
	"wx/gdiobj.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\brush.h
	"wx/gdicmn.h"
	"wx/gdiobj.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\brush.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dc.h
	"wx/object.h"
	"wx/intl.h"
	"wx/cursor.h"
	"wx/font.h"
	"wx/colour.h"
	"wx/bitmap.h"
	"wx/brush.h"
	"wx/pen.h"
	"wx/palette.h"
	"wx/dynarray.h"
	"wx/math.h"
	"wx/image.h"
	"wx/region.h"
	"wx/affinematrix2d.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\pen.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/msw/pen.h"
	"wx/x11/pen.h"
	"wx/gtk/pen.h"
	"wx/gtk1/pen.h"
	"wx/dfb/pen.h"
	"wx/osx/pen.h"
	"wx/cocoa/pen.h"
	"wx/os2/pen.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\pen.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\pen.h
	"wx/gdicmn.h"
	"wx/gdiobj.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\pen.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\pen.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\pen.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\pen.h
	"wx/gdiobj.h"
	"wx/colour.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\pen.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\pen.h
	"wx/gdiobj.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\affinematrix2d.h
	"wx/defs.h"
	"wx/affinematrix2dbase.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\affinematrix2dbase.h
	"wx/defs.h"
	"wx/geometry.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\geometry.h
	"wx/defs.h"
	"wx/utils.h"
	"wx/gdicmn.h"
	"wx/math.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\window.h
	"wx/cocoa/NSView.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nsview.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\objcassociate.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\window.h
	<os2.h>
	"wx/hash.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\window.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\control.h
	"wx/univ/inphand.h"
	"wx/univ/inpcons.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\inphand.h
	"wx/univ/inpcons.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\inpcons.h
	"wx/object.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\control.h
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\control.h
	"wx/window.h"
	"wx/list.h"
	"wx/validate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\control.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\control.h
	"wx/defs.h"
	"wx/object.h"
	"wx/list.h"
	"wx/window.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\control.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\control.h
	"wx/cocoa/NSControl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nscontrol.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\control.h
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\textentry.h
	"wx/filefn.h"
	"wx/gdicmn.h"
	"wx/gtk/textentry.h"
	"wx/osx/textentry.h"
	"wx/msw/textentry.h"
	"wx/motif/textentry.h"
	"wx/os2/textentry.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\textentry.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\textentry.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\textentry.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\textentry.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\textentry.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\ioswrap.h
	"wx/beforestd.h"
	<iostream.h>
	<iostream>
	"wx/afterstd.h"
	"wx/msw/winundef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\textctrl.h
	"wx/univ/textctrl.h"
	"wx/scrolwin.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\textctrl.h
	"wx/scrolwin.h"
	"wx/univ/inphand.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\scrolwin.h
	"wx/panel.h"
	"wx/gtk/scrolwin.h"
	"wx/gtk1/scrolwin.h"
	"wx/generic/scrolwin.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\panel.h
	"wx/window.h"
	"wx/containr.h"
	"wx/univ/panel.h"
	"wx/msw/panel.h"
	"wx/generic/panelg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\containr.h
	"wx/defs.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\panel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\panel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\panelg.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\scrolwin.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\scrolwin.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\scrolwin.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\textctrlce.h
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\textctrl.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\textctrl.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\textctrl.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\textctrl.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\textctrl.h
	"wx/control.h"
	"wx/textctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\textctrl.h
	"wx/cocoa/NSTextField.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nstextfield.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\textctrl.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dialog.h
	"wx/toplevel.h"
	"wx/containr.h"
	"wx/sharedptr.h"
	"wx/univ/dialog.h"
	"wx/msw/dialog.h"
	"wx/motif/dialog.h"
	"wx/gtk/dialog.h"
	"wx/gtk1/dialog.h"
	"wx/osx/dialog.h"
	"wx/cocoa/dialog.h"
	"wx/os2/dialog.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\toplevel.h
	"wx/nonownedwnd.h"
	"wx/iconbndl.h"
	"wx/weakref.h"
	"wx/msw/toplevel.h"
	"wx/gtk/toplevel.h"
	"wx/gtk1/toplevel.h"
	"wx/x11/toplevel.h"
	"wx/dfb/toplevel.h"
	"wx/osx/toplevel.h"
	"wx/cocoa/toplevel.h"
	"wx/os2/toplevel.h"
	"wx/motif/toplevel.h"
	"wx/univ/toplevel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\nonownedwnd.h
	"wx/window.h"
	"wx/dfb/nonownedwnd.h"
	"wx/gtk/nonownedwnd.h"
	"wx/osx/nonownedwnd.h"
	"wx/msw/nonownedwnd.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\nonownedwnd.h
	"wx/window.h"
	"wx/dfb/dfbptr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\nonownedwnd.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\nonownedwnd.h
	"wx/window.h"
	"wx/graphics.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\graphics.h
	"wx/defs.h"
	"wx/geometry.h"
	"wx/dynarray.h"
	"wx/dc.h"
	"wx/image.h"
	"wx/vector.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\nonownedwnd.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\iconbndl.h
	"wx/gdiobj.h"
	"wx/gdicmn.h"
	"wx/icon.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\icon.h
	"wx/iconloc.h"
	"wx/msw/icon.h"
	"wx/motif/icon.h"
	"wx/generic/icon.h"
	"wx/generic/icon.h"
	"wx/generic/icon.h"
	"wx/generic/icon.h"
	"wx/osx/icon.h"
	"wx/generic/icon.h"
	"wx/cocoa/icon.h"
	"wx/os2/icon.h"
	"wx/variant.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\iconloc.h
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\icon.h
	"wx/msw/gdiimage.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\icon.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\icon.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\icon.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\icon.h
	"wx/gdicmn.h"
	"wx/gdiobj.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\icon.h
	"wx/bitmap.h"
	"wx/os2/gdiimage.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\weakref.h
	"wx/tracker.h"
	"wx/meta/convertible.h"
	"wx/meta/int2type.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\meta\int2type.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\toplevel.h
	"wx/hashmap.h"
	"wx/cocoa/NSWindow.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nswindow.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\toplevel.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\toplevel.h
	"wx/univ/inpcons.h"
	"wx/univ/inphand.h"
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\sharedptr.h
	"wx/defs.h"
	"wx/atomic.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\atomic.h
	"wx/defs.h"
	"wx/msw/wrapwin.h"
	"libkern/OSAtomic.h"
	<atomic.h>
	"wx/thread.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\dialog.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\dialog.h
	"wx/panel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\dialog.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\dialog.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\dialog.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\dialog.h
	"wx/panel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\dialog.h
	"wx/defs.h"
	"wx/panel.h"
	"wx/cocoa/NSPanel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nspanel.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\dialog.h
	"wx/panel.h"

1668650196 e:\tck.w_client_iot\include\yblib.h

1559628924 source:e:\tck.w_client_iot\mainframe_gk.cpp
	"MainFrame_GK.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/msgdlg.h>
	"DebugWin.h"
	"tcp_client.h"

1559641601 e:\tck.w_client_iot\mainframe_gk.h
	<wx/button.h>
	<wx/frame.h>
	<wx/grid.h>
	<wx/panel.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/timer.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\stattext.h
	"wx/defs.h"
	"wx/control.h"
	"wx/univ/stattext.h"
	"wx/msw/stattext.h"
	"wx/motif/stattext.h"
	"wx/gtk/stattext.h"
	"wx/gtk1/stattext.h"
	"wx/osx/stattext.h"
	"wx/cocoa/stattext.h"
	"wx/os2/stattext.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\stattext.h
	"wx/generic/stattextg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\stattextg.h
	"wx/stattext.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\stattext.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\stattext.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\stattext.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\stattext.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\stattext.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\stattext.h
	"wx/cocoa/NSTextField.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\stattext.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\statbox.h
	"wx/defs.h"
	"wx/control.h"
	"wx/containr.h"
	"wx/univ/statbox.h"
	"wx/msw/statbox.h"
	"wx/motif/statbox.h"
	"wx/gtk/statbox.h"
	"wx/gtk1/statbox.h"
	"wx/osx/statbox.h"
	"wx/cocoa/statbox.h"
	"wx/os2/statbox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\statbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\statbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\statbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\statbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\statbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\statbox.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\statbox.h
	"wx/cocoa/NSBox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nsbox.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\statbox.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\grid.h
	"wx/generic/grid.h"
	"wx/generic/grideditors.h"
	"wx/generic/gridctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\grid.h
	"wx/defs.h"
	"wx/hashmap.h"
	"wx/scrolwin.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\grideditors.h
	"wx/defs.h"
	"wx/scopedptr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\gridctrl.h
	"wx/grid.h"
	"wx/datetime.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\button.h
	"wx/defs.h"
	"wx/anybutton.h"
	"wx/univ/button.h"
	"wx/msw/button.h"
	"wx/motif/button.h"
	"wx/gtk/button.h"
	"wx/gtk1/button.h"
	"wx/osx/button.h"
	"wx/cocoa/button.h"
	"wx/os2/button.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\anybutton.h
	"wx/defs.h"
	"wx/bitmap.h"
	"wx/control.h"
	"wx/univ/anybutton.h"
	"wx/msw/anybutton.h"
	"wx/gtk/anybutton.h"
	"wx/osx/anybutton.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\anybutton.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\anybutton.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\anybutton.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\anybutton.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\button.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\button.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\button.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\button.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\button.h
	"wx/defs.h"
	"wx/object.h"
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\button.h
	"wx/control.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\button.h
	"wx/cocoa/NSButton.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nsbutton.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"
	"wx/cocoa/ObjcRef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\objcref.h
	"wx/osx/core/cfref.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\button.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\frame.h
	"wx/toplevel.h"
	"wx/statusbr.h"
	"wx/univ/frame.h"
	"wx/msw/frame.h"
	"wx/gtk/frame.h"
	"wx/gtk1/frame.h"
	"wx/motif/frame.h"
	"wx/osx/frame.h"
	"wx/cocoa/frame.h"
	"wx/os2/frame.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\statusbr.h
	"wx/defs.h"
	"wx/control.h"
	"wx/list.h"
	"wx/dynarray.h"
	"wx/univ/statusbr.h"
	"wx/msw/statusbar.h"
	"wx/generic/statusbr.h"
	"wx/osx/statusbr.h"
	"wx/generic/statusbr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\statusbr.h
	"wx/univ/inpcons.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\statusbar.h
	"wx/vector.h"
	"wx/tooltip.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\tooltip.h
	"wx/defs.h"
	"wx/msw/tooltip.h"
	"wx/gtk/tooltip.h"
	"wx/gtk1/tooltip.h"
	"wx/osx/tooltip.h"
	"wx/cocoa/tooltip.h"
	"wx/os2/tooltip.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\tooltip.h
	"wx/object.h"
	"wx/gdicmn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\tooltip.h
	"wx/string.h"
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\tooltip.h
	"wx/defs.h"
	"wx/string.h"
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\tooltip.h
	"wx/string.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\tooltip.h
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\tooltip.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\statusbr.h
	"wx/defs.h"
	"wx/pen.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\statusbr.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\frame.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\frame.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\frame.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\frame.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\frame.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\frame.h
	"wx/toolbar.h"
	"wx/accel.h"
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\toolbar.h
	"wx/defs.h"
	"wx/tbarbase.h"
	"wx/univ/toolbar.h"
	"wx/msw/toolbar.h"
	"wx/msw/wince/tbarwce.h"
	"wx/motif/toolbar.h"
	"wx/gtk/toolbar.h"
	"wx/gtk1/toolbar.h"
	"wx/osx/toolbar.h"
	"wx/cocoa/toolbar.h"
	"wx/os2/toolbar.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\tbarbase.h
	"wx/defs.h"
	"wx/bitmap.h"
	"wx/list.h"
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\toolbar.h
	"wx/button.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\toolbar.h
	"wx/dynarray.h"
	"wx/imaglist.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\imaglist.h
	"wx/defs.h"
	"wx/generic/imaglist.h"
	"wx/msw/imaglist.h"
	"wx/osx/imaglist.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\imaglist.h
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\imaglist.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\imaglist.h
	"wx/defs.h"
	"wx/list.h"
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\tbarwce.h
	"wx/dynarray.h"
	"wx/msw/toolbar.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\toolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\toolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\toolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\toolbar.h
	"wx/tbarbase.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\toolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\toolbar.h
	"wx/timer.h"
	"wx/tbarbase.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\timer.h
	"wx/defs.h"
	"wx/object.h"
	"wx/longlong.h"
	"wx/event.h"
	"wx/stopwatch.h"
	"wx/utils.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\stopwatch.h
	"wx/defs.h"
	"wx/longlong.h"
	"wx/time.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\time.h
	"wx/longlong.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\frame.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\frame.h
	"wx/os2/wxrsc.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\wxrsc.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msgdlg.h
	"wx/defs.h"
	"wx/dialog.h"
	"wx/stockitem.h"
	"wx/generic/msgdlgg.h"
	"wx/cocoa/msgdlg.h"
	"wx/msw/msgdlg.h"
	"wx/motif/msgdlg.h"
	"wx/gtk/msgdlg.h"
	"wx/osx/msgdlg.h"
	"wx/os2/msgdlg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\stockitem.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/string.h"
	"wx/accel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\msgdlgg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\msgdlg.h
	"wx/msgdlg.h"
	"wx/generic/msgdlgg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\msgdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\msgdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\msgdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\msgdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\msgdlg.h

********** e:\tck.w_client_iot\tcp_client.h
	"yblib.h"
	<string>

1663728466 source:e:\tck.w_client_iot\resource.rc
	"wx/msw/wx.rc"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wx.rc
	<windows.h>
	"wx/msw/wince/wince.rc"
	"wx/msw/rcdefs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\wince.rc
	<commctrl.h>
	"wx/msw/wince/resources.h"
	"wx/msw/wince/smartphone.rc"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\resources.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\smartphone.rc
	<aygshell.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\rcdefs.h

1738332807 source:e:\tck.w_client_iot\tck_w_client_iotapp.cpp
	"TCK_W_Client_IoTApp.h"
	"TCK_W_Client_IoTMain.h"
	<wx/image.h>
	"DebugWin.h"
	"tcp_client_web.h"
	"NoteWin.h"
	"MainFrame.h"
	<wx/msgdlg.h>
	<wx/xrc/xmlres.h>
	"JPG/Resources.h"
	"TCK_WR_LANG.h"
	<direct.h>
	<TlHelp32.h>
	<iphlpapi.h>
	<wx/thread.h>
	"tcp_client_view.h"
	<unistd.h>
	<sys/time.h>

1662018134 e:\tck.w_client_iot\tck_w_client_iotapp.h
	<wx/app.h>
	<wx/snglinst.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\app.h
	"wx/event.h"
	"wx/eventfilter.h"
	"wx/build.h"
	"wx/cmdargs.h"
	"wx/init.h"
	"wx/intl.h"
	"wx/log.h"
	"wx/unix/app.h"
	"wx/msw/app.h"
	"wx/motif/app.h"
	"wx/dfb/app.h"
	"wx/gtk/app.h"
	"wx/gtk1/app.h"
	"wx/x11/app.h"
	"wx/osx/app.h"
	"wx/cocoa/app.h"
	"wx/os2/app.h"
	"wx/univ/theme.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\eventfilter.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\build.h
	"wx/version.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cmdargs.h
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\init.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/msw/init.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\init.h
	"wx/msw/wrapwin.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\unix\app.h
	<signal.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\app.h
	"wx/event.h"
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\app.h
	"wx/event.h"
	"wx/hashmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dfb\app.h
	"wx/dfb/dfbptr.h"
	"wx/vidmode.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\vidmode.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\app.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\app.h
	"wx/frame.h"
	"wx/icon.h"
	"wx/strconv.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\app.h
	"wx/gdicmn.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\app.h
	"wx/defs.h"
	"wx/object.h"
	"wx/gdicmn.h"
	"wx/event.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\app.h
	"wx/osx/core/cfref.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\app.h
	<types.h>
	<sys/ioctl.h>
	<sys/select.h>
	<sys/time.h>
	<sys/types.h>
	<unistd.h>
	<utils.h>
	<types.h>
	"wx/event.h"
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\theme.h
	"wx/string.h"

1660281769 e:\tck.w_client_iot\tck_w_client_iotmain.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/stattext.h>
	<wx/textctrl.h>

1665561513 source:e:\tck.w_client_iot\tck_w_client_iotmain.cpp
	"TCK_W_Client_IoTMain.h"
	<wx/msgdlg.h>
	<wx/xrc/xmlres.h>
	<wx/intl.h>
	<wx/string.h>
	"tcp_client_web.h"

********** source:e:\tck.w_client_iot\tcp_client.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client.h"
	"DebugWin.h"
	<wx/event.h>

********** source:e:\tck.w_client_iot\mainframe.cpp
	"MainFrame.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	<wx/msgdlg.h>
	"DebugWin.h"
	"TCK_W_Client_IoTMain.h"
	"BeltFrame.h"
	"TCK_WR_LANG.h"
	"Validity.h"
	<wx/translation.h>
	"AccountDialog.h"

********** e:\tck.w_client_iot\mainframe.h
	<wx/frame.h>
	<wx/panel.h>
	<wx/scrolwin.h>
	<wx/timer.h>
	<wx/sizer.h>
	<wx/stattext.h>
	<wx/statbmp.h>
	"tcp_client_web.h"
	<map>
	"Control/TransStaticText.h"
	"Control/TransStaticBmp.h"
	"Control/BitmapButton.h"

********** source:d:\tck\tck.w_client_iot\debugwin.cpp
	"DebugWin.h"
	<wx/intl.h>
	<wx/string.h>

********** d:\tck\tck.w_client_iot\debugwin.h
	<wx/dialog.h>
	<wx/textctrl.h>
	"yblib.h"

********** d:\tck\tck.w_client_iot\include\yblib.h

********** source:d:\tck\tck.w_client_iot\mainframe.cpp
	"MainFrame.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/msgdlg.h>
	"DebugWin.h"
	"tcp_client_web.h"
	"tcp_client_view.h"
	<wx/dcmemory.h>
	<vector>

********** d:\tck\tck.w_client_iot\mainframe.h
	<wx/button.h>
	<wx/frame.h>
	<wx/grid.h>
	<wx/panel.h>
	<wx/statbmp.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/timer.h>

********** d:\tck\tck.w_client_iot\tcp_client.h
	"yblib.h"
	<string>

********** source:d:\tck\tck.w_client_iot\resource.rc
	"wx/msw/wx.rc"

1559923125 source:d:\tck\tck.w_client_iot\tck_w_client_iotapp.cpp
	"TCK_W_Client_IoTApp.h"
	"TCK_W_Client_IoTMain.h"
	<wx/image.h>
	"DebugWin.h"
	"tcp_client_web.h"
	"tcp_client_view.h"
	"MainFrame.h"

1544061540 d:\tck\tck.w_client_iot\tck_w_client_iotapp.h
	<wx/app.h>

1544063908 d:\tck\tck.w_client_iot\tck_w_client_iotmain.h
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/button.h>
	<wx/dialog.h>

1559977450 source:d:\tck\tck.w_client_iot\tck_w_client_iotmain.cpp
	"TCK_W_Client_IoTMain.h"
	<wx/msgdlg.h>
	<wx/intl.h>
	<wx/string.h>
	"tcp_client_web.h"
	"tcp_client_view.h"

********** source:d:\tck\tck.w_client_iot\tcp_client.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client.h"
	"DebugWin.h"
	<wx/event.h>

1559921297 d:\tck\tck.w_client_iot\tcp_client_web.h
	"yblib.h"
	<string>

1559963611 source:d:\tck\tck.w_client_iot\tcp_client_web.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client_web.h"
	"DebugWin.h"
	<wx/event.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\statbmp.h
	"wx/defs.h"
	"wx/control.h"
	"wx/bitmap.h"
	"wx/icon.h"
	"wx/univ/statbmp.h"
	"wx/msw/statbmp.h"
	"wx/motif/statbmp.h"
	"wx/gtk/statbmp.h"
	"wx/gtk1/statbmp.h"
	"wx/osx/statbmp.h"
	"wx/cocoa/statbmp.h"
	"wx/os2/statbmp.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\statbmp.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\statbmp.h
	"wx/control.h"
	"wx/icon.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\statbmp.h
	"wx/motif/bmpmotif.h"
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\bmpmotif.h
	"wx/defs.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\statbmp.h
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\statbmp.h
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\statbmp.h
	"wx/osx/carbon/statbmp.h"
	"wx/generic/statbmpg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\carbon\statbmp.h
	"wx/icon.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\statbmpg.h
	"wx/statbmp.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\statbmp.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\statbmp.h
	"wx/control.h"
	"wx/icon.h"

1559962846 d:\tck\tck.w_client_iot\tcp_client_view.h
	"yblib.h"
	<string>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dcmemory.h
	"wx/dc.h"
	"wx/bitmap.h"

1559977517 source:d:\tck\tck.w_client_iot\tcp_client_view.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client_view.h"
	"DebugWin.h"
	<wx/event.h>

1715075041 e:\tck.w_client_iot\tcp_client_web.h
	"yblib.h"
	<string>

1664257049 e:\tck.w_client_iot\tcp_client_view.h
	"yblib.h"
	<string>
	<wx/event.h>

1744193712 source:e:\tck.w_client_iot\tcp_client_view.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client_view.h"
	"DebugWin.h"

1744193710 source:e:\tck.w_client_iot\tcp_client_web.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client_web.h"
	"DebugWin.h"
	<wx/event.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\mstream.h
	"wx/defs.h"
	"wx/stream.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\listbox.h
	"wx/defs.h"
	"wx/ctrlsub.h"
	"wx/univ/listbox.h"
	"wx/msw/listbox.h"
	"wx/motif/listbox.h"
	"wx/gtk/listbox.h"
	"wx/gtk1/listbox.h"
	"wx/osx/listbox.h"
	"wx/os2/listbox.h"
	"wx/cocoa/listbox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\ctrlsub.h
	"wx/defs.h"
	"wx/arrstr.h"
	"wx/control.h"
	"wx/msw/ctrlsub.h"
	"wx/motif/ctrlsub.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\ctrlsub.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\ctrlsub.h
	"wx/dynarray.h"
	"wx/generic/ctrlsub.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\ctrlsub.h
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\listbox.h
	"wx/scrolwin.h"
	"wx/dynarray.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\listbox.h
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\listbox.h
	"wx/ctrlsub.h"
	"wx/clntdata.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\listbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\listbox.h
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\listbox.h
	"wx/dynarray.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\listbox.h
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\listbox.h
	"wx/cocoa/NSTableView.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nstableview.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

1587442079 e:\tck.w_client_iot\querydata.h
	<wx/button.h>
	<wx/frame.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/listbox.h>
	<wx/panel.h>
	"yblib.h"
	<string>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dcclient.h
	"wx/dc.h"

1587442828 source:e:\tck.w_client_iot\querydata.cpp
	"QueryData.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/msgdlg.h>
	"tcp_client_web.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gauge.h
	"wx/defs.h"
	"wx/control.h"
	"wx/univ/gauge.h"
	"wx/msw/gauge.h"
	"wx/motif/gauge.h"
	"wx/gtk/gauge.h"
	"wx/gtk1/gauge.h"
	"wx/osx/gauge.h"
	"wx/cocoa/gauge.h"
	"wx/os2/gauge.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\gauge.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\gauge.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\gauge.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\gauge.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\gauge.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\gauge.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\gauge.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\gauge.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\sizer.h
	"wx/defs.h"
	"wx/window.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xmlres.h
	"wx/defs.h"
	"wx/string.h"
	"wx/dynarray.h"
	"wx/arrstr.h"
	"wx/datetime.h"
	"wx/list.h"
	"wx/gdicmn.h"
	"wx/filesys.h"
	"wx/bitmap.h"
	"wx/icon.h"
	"wx/artprov.h"
	"wx/colour.h"
	"wx/vector.h"
	"wx/xrc/xmlreshandler.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\filesys.h
	"wx/defs.h"
	"wx/stream.h"
	"wx/datetime.h"
	"wx/filename.h"
	"wx/hashmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\filename.h
	"wx/arrstr.h"
	"wx/filefn.h"
	"wx/datetime.h"
	"wx/intl.h"
	"wx/longlong.h"
	"wx/file.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\file.h
	"wx/defs.h"
	"wx/string.h"
	"wx/filefn.h"
	"wx/convauto.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\convauto.h
	"wx/strconv.h"
	"wx/fontenc.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\artprov.h
	"wx/string.h"
	"wx/bitmap.h"
	"wx/icon.h"
	"wx/iconbndl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xmlreshandler.h
	"wx/defs.h"
	"wx/string.h"
	"wx/artprov.h"
	"wx/colour.h"
	"wx/filesys.h"
	"wx/imaglist.h"
	"wx/window.h"

1559281538 e:\tck.w_client_iot\tck_wr_lang.h
	<wx/strconv.h>

1660283456 source:e:\tck.w_client_iot\notewin.cpp
	"NoteWin.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	<wx/snglinst.h>
	"TCK_WR_LANG.h"

1658134217 e:\tck.w_client_iot\notewin.h
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/checkbox.h>
	<wx/panel.h>
	<wx/button.h>
	<wx/dialog.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\checkbox.h
	"wx/defs.h"
	"wx/control.h"
	"wx/univ/checkbox.h"
	"wx/msw/checkbox.h"
	"wx/motif/checkbox.h"
	"wx/gtk/checkbox.h"
	"wx/gtk1/checkbox.h"
	"wx/osx/checkbox.h"
	"wx/cocoa/checkbox.h"
	"wx/os2/checkbox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\checkbox.h
	"wx/button.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\checkbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\checkbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\checkbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\checkbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\checkbox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\checkbox.h
	"wx/cocoa/NSButton.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\checkbox.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\snglinst.h
	"wx/app.h"
	"wx/utils.h"

1747278647 e:\tck.w_client_iot\jpg\resources.h
	<wx/wxprec.h>
	<wx/filesys.h>
	<wx/fs_mem.h>
	<wx/xrc/xmlres.h>
	<wx/xrc/xh_all.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\wxprec.h
	"wx/defs.h"
	"wx/chartype.h"
	"wx/msw/wrapwin.h"
	"wx/msw/private.h"
	"wx/msw/wrapcctl.h"
	"wx/msw/wrapcdlg.h"
	"wx/msw/missing.h"
	"wx/os2/private.h"
	"wx/wx.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wrapcctl.h
	"wx/msw/wrapwin.h"
	<commctrl.h>
	"wx/msw/missing.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\missing.h
	<windows.h>
	"wx/msw/winundef.h"
	"wx/msw/wince/missing.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\missing.h
	"wx/msw/private.h"
	"shellapi.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wrapcdlg.h
	"wx/defs.h"
	"wx/msw/wrapwin.h"
	"wx/msw/private.h"
	"wx/msw/missing.h"
	<commdlg.h>
	"wx/msw/winundef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\wx.h
	"wx/defs.h"
	"wx/object.h"
	"wx/dynarray.h"
	"wx/list.h"
	"wx/hash.h"
	"wx/string.h"
	"wx/hashmap.h"
	"wx/arrstr.h"
	"wx/intl.h"
	"wx/log.h"
	"wx/event.h"
	"wx/app.h"
	"wx/utils.h"
	"wx/stream.h"
	"wx/memory.h"
	"wx/math.h"
	"wx/stopwatch.h"
	"wx/timer.h"
	"wx/module.h"
	"wx/wxcrt.h"
	"wx/wxcrtvararg.h"
	"wx/window.h"
	"wx/containr.h"
	"wx/panel.h"
	"wx/toplevel.h"
	"wx/frame.h"
	"wx/gdicmn.h"
	"wx/gdiobj.h"
	"wx/region.h"
	"wx/bitmap.h"
	"wx/image.h"
	"wx/colour.h"
	"wx/font.h"
	"wx/dc.h"
	"wx/dcclient.h"
	"wx/dcmemory.h"
	"wx/dcprint.h"
	"wx/dcscreen.h"
	"wx/button.h"
	"wx/menuitem.h"
	"wx/menu.h"
	"wx/pen.h"
	"wx/brush.h"
	"wx/palette.h"
	"wx/icon.h"
	"wx/cursor.h"
	"wx/dialog.h"
	"wx/settings.h"
	"wx/msgdlg.h"
	"wx/dataobj.h"
	"wx/control.h"
	"wx/ctrlsub.h"
	"wx/bmpbuttn.h"
	"wx/checkbox.h"
	"wx/checklst.h"
	"wx/choice.h"
	"wx/scrolbar.h"
	"wx/stattext.h"
	"wx/statbmp.h"
	"wx/statbox.h"
	"wx/listbox.h"
	"wx/radiobox.h"
	"wx/radiobut.h"
	"wx/textctrl.h"
	"wx/slider.h"
	"wx/gauge.h"
	"wx/scrolwin.h"
	"wx/dirdlg.h"
	"wx/toolbar.h"
	"wx/combobox.h"
	"wx/layout.h"
	"wx/sizer.h"
	"wx/statusbr.h"
	"wx/choicdlg.h"
	"wx/textdlg.h"
	"wx/filedlg.h"
	"wx/mdi.h"
	"wx/validate.h"
	"wx/valtext.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\module.h
	"wx/object.h"
	"wx/list.h"
	"wx/arrstr.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dcprint.h
	"wx/defs.h"
	"wx/dc.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dcscreen.h
	"wx/defs.h"
	"wx/dc.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\menuitem.h
	"wx/defs.h"
	"wx/object.h"
	"wx/univ/menuitem.h"
	"wx/msw/menuitem.h"
	"wx/motif/menuitem.h"
	"wx/gtk/menuitem.h"
	"wx/gtk1/menuitem.h"
	"wx/osx/menuitem.h"
	"wx/cocoa/menuitem.h"
	"wx/os2/menuitem.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\menuitem.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\menuitem.h
	"wx/ownerdrw.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\ownerdrw.h
	"wx/defs.h"
	"wx/font.h"
	"wx/colour.h"
	"wx/msw/ownerdrw.h"
	"wx/os2/ownerdrw.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\ownerdrw.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\ownerdrw.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\menuitem.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\menuitem.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\menuitem.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\menuitem.h
	"wx/defs.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\menuitem.h
	"wx/hashmap.h"
	"wx/bitmap.h"
	"wx/cocoa/ObjcRef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\menuitem.h
	"wx/defs.h"
	"wx/os2/private.h"
	"wx/ownerdrw.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\menu.h
	"wx/defs.h"
	"wx/list.h"
	"wx/window.h"
	"wx/menuitem.h"
	"wx/univ/menu.h"
	"wx/msw/menu.h"
	"wx/motif/menu.h"
	"wx/gtk/menu.h"
	"wx/gtk1/menu.h"
	"wx/osx/menu.h"
	"wx/cocoa/menu.h"
	"wx/os2/menu.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\menu.h
	"wx/accel.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\menu.h
	"wx/accel.h"
	"wx/dynarray.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\menu.h
	"wx/colour.h"
	"wx/font.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\menu.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\menu.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\menu.h
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\menu.h
	"wx/cocoa/NSMenu.h"
	"wx/accel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nsmenu.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\menu.h
	"wx/accel.h"
	"wx/list.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dataobj.h
	"wx/defs.h"
	"wx/string.h"
	"wx/bitmap.h"
	"wx/list.h"
	"wx/arrstr.h"
	"wx/msw/ole/dataform.h"
	"wx/motif/dataform.h"
	"wx/gtk/dataform.h"
	"wx/gtk1/dataform.h"
	"wx/x11/dataform.h"
	"wx/osx/dataform.h"
	"wx/cocoa/dataform.h"
	"wx/os2/dataform.h"
	"wx/msw/ole/dataobj.h"
	"wx/motif/dataobj.h"
	"wx/x11/dataobj.h"
	"wx/gtk/dataobj.h"
	"wx/gtk1/dataobj.h"
	"wx/osx/dataobj.h"
	"wx/cocoa/dataobj.h"
	"wx/os2/dataobj.h"
	"wx/msw/ole/dataobj2.h"
	"wx/gtk/dataobj2.h"
	"wx/gtk1/dataobj2.h"
	"wx/x11/dataobj2.h"
	"wx/motif/dataobj2.h"
	"wx/osx/dataobj2.h"
	"wx/cocoa/dataobj2.h"
	"wx/os2/dataobj2.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\ole\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\dataform.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\ole\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\dataobj.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\ole\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\x11\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\dataobj2.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\bmpbuttn.h
	"wx/defs.h"
	"wx/button.h"
	"wx/univ/bmpbuttn.h"
	"wx/msw/bmpbuttn.h"
	"wx/motif/bmpbuttn.h"
	"wx/gtk/bmpbuttn.h"
	"wx/gtk1/bmpbuttn.h"
	"wx/osx/bmpbuttn.h"
	"wx/cocoa/bmpbuttn.h"
	"wx/os2/bmpbuttn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\bmpbuttn.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\bmpbuttn.h
	"wx/button.h"
	"wx/bitmap.h"
	"wx/brush.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\bmpbuttn.h
	"wx/motif/bmpmotif.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\bmpbuttn.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\bmpbuttn.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\bmpbuttn.h
	"wx/button.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\bmpbuttn.h
	"wx/cocoa/NSButton.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\bmpbuttn.h
	"wx/button.h"
	"wx/dcclient.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\checklst.h
	"wx/defs.h"
	"wx/listbox.h"
	"wx/univ/checklst.h"
	"wx/msw/wince/checklst.h"
	"wx/msw/checklst.h"
	"wx/motif/checklst.h"
	"wx/gtk/checklst.h"
	"wx/gtk1/checklst.h"
	"wx/osx/checklst.h"
	"wx/cocoa/checklst.h"
	"wx/os2/checklst.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\checklst.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\checklst.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\checklst.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\checklst.h
	"wx/listbox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\checklst.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\checklst.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\checklst.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\checklst.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\checklst.h
	<stddef.h>
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\choice.h
	"wx/defs.h"
	"wx/ctrlsub.h"
	"wx/univ/choice.h"
	"wx/msw/wince/choicece.h"
	"wx/msw/choice.h"
	"wx/motif/choice.h"
	"wx/gtk/choice.h"
	"wx/gtk1/choice.h"
	"wx/osx/choice.h"
	"wx/cocoa/choice.h"
	"wx/os2/choice.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\choice.h
	"wx/combobox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\combobox.h
	"wx/defs.h"
	"wx/textctrl.h"
	"wx/ctrlsub.h"
	"wx/textentry.h"
	"wx/univ/combobox.h"
	"wx/msw/combobox.h"
	"wx/motif/combobox.h"
	"wx/gtk/combobox.h"
	"wx/gtk1/combobox.h"
	"wx/osx/combobox.h"
	"wx/cocoa/combobox.h"
	"wx/os2/combobox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\combobox.h
	"wx/combo.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\combo.h
	"wx/defs.h"
	"wx/control.h"
	"wx/renderer.h"
	"wx/bitmap.h"
	"wx/textentry.h"
	"wx/msw/combo.h"
	"wx/generic/combo.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\renderer.h
	"wx/gdicmn.h"
	"wx/colour.h"
	"wx/font.h"
	"wx/bitmap.h"
	"wx/string.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\combo.h
	"wx/timer.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\combo.h
	"wx/dcbuffer.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dcbuffer.h
	"wx/dcmemory.h"
	"wx/dcclient.h"
	"wx/window.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\combobox.h
	"wx/choice.h"
	"wx/textentry.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\combobox.h
	"wx/choice.h"
	"wx/textentry.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\combobox.h
	"wx/choice.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\combobox.h
	"wx/defs.h"
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\combobox.h
	"wx/containr.h"
	"wx/choice.h"
	"wx/textctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\combobox.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"
	"wx/textctrl.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\combobox.h
	"wx/choice.h"
	"wx/textentry.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\wince\choicece.h
	"wx/defs.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\choice.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\choice.h
	"wx/clntdata.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\choice.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\choice.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\choice.h
	"wx/control.h"
	"wx/dynarray.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\choice.h
	"wx/cocoa/NSMenu.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\choice.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\scrolbar.h
	"wx/defs.h"
	"wx/control.h"
	"wx/univ/scrolbar.h"
	"wx/msw/scrolbar.h"
	"wx/motif/scrolbar.h"
	"wx/gtk/scrolbar.h"
	"wx/gtk1/scrolbar.h"
	"wx/osx/scrolbar.h"
	"wx/cocoa/scrolbar.h"
	"wx/os2/scrolbar.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\scrolbar.h
	"wx/univ/scrarrow.h"
	"wx/renderer.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\scrarrow.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\scrolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\scrolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\scrolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\scrolbar.h
	"wx/defs.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\scrolbar.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\scrolbar.h
	"wx/cocoa/NSScroller.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nsscroller.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"
	"wx/cocoa/ObjcRef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\scrolbar.h
	"wx/scrolbar.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\radiobox.h
	"wx/defs.h"
	"wx/ctrlsub.h"
	"wx/dynarray.h"
	"wx/univ/radiobox.h"
	"wx/msw/radiobox.h"
	"wx/motif/radiobox.h"
	"wx/gtk/radiobox.h"
	"wx/gtk1/radiobox.h"
	"wx/osx/radiobox.h"
	"wx/cocoa/radiobox.h"
	"wx/os2/radiobox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\radiobox.h
	"wx/statbox.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\radiobox.h
	"wx/statbox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\radiobox.h
	"wx/dynarray.h"
	"wx/arrstr.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\radiobox.h
	"wx/bitmap.h"
	"wx/list.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\radiobox.h
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\radiobox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\radiobox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\radiobox.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\radiobut.h
	"wx/defs.h"
	"wx/control.h"
	"wx/univ/radiobut.h"
	"wx/msw/radiobut.h"
	"wx/motif/radiobut.h"
	"wx/gtk/radiobut.h"
	"wx/gtk1/radiobut.h"
	"wx/osx/radiobut.h"
	"wx/cocoa/radiobut.h"
	"wx/os2/radiobut.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\radiobut.h
	"wx/checkbox.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\radiobut.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\radiobut.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\radiobut.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\radiobut.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\radiobut.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\radiobut.h
	"wx/cocoa/NSButton.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\radiobut.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\slider.h
	"wx/defs.h"
	"wx/control.h"
	"wx/univ/slider.h"
	"wx/msw/slider.h"
	"wx/motif/slider.h"
	"wx/gtk/slider.h"
	"wx/gtk1/slider.h"
	"wx/osx/slider.h"
	"wx/cocoa/slider.h"
	"wx/os2/slider.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\slider.h
	"wx/univ/scrthumb.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\univ\scrthumb.h
	"wx/timer.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\slider.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\slider.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\slider.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\slider.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\slider.h
	"wx/control.h"
	"wx/slider.h"
	"wx/stattext.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\slider.h
	"wx/cocoa/NSSlider.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\nsslider.h
	"wx/hashmap.h"
	"wx/cocoa/ObjcAssociate.h"
	"wx/cocoa/ObjcRef.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\slider.h
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dirdlg.h
	"wx/dialog.h"
	"wx/generic/dirdlgg.h"
	"wx/generic/dirdlgg.h"
	"wx/generic/dirdlgg.h"
	"wx/msw/dirdlg.h"
	"wx/gtk/dirdlg.h"
	"wx/generic/dirdlgg.h"
	"wx/osx/dirdlg.h"
	"wx/cocoa/dirdlg.h"
	"wx/generic/dirdlgg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\dirdlgg.h
	"wx/dialog.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\dirdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\dirdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\dirdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\dirdlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\layout.h
	"wx/object.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\choicdlg.h
	"wx/defs.h"
	"wx/generic/choicdgg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\choicdgg.h
	"wx/dynarray.h"
	"wx/dialog.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\textdlg.h
	"wx/generic/textdlgg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\textdlgg.h
	"wx/defs.h"
	"wx/dialog.h"
	"wx/valtext.h"
	"wx/textctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\valtext.h
	"wx/defs.h"
	"wx/validate.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\filedlg.h
	"wx/defs.h"
	"wx/dialog.h"
	"wx/arrstr.h"
	"wx/generic/filedlgg.h"
	"wx/msw/filedlg.h"
	"wx/motif/filedlg.h"
	"wx/gtk/filedlg.h"
	"wx/gtk1/filedlg.h"
	"wx/osx/filedlg.h"
	"wx/cocoa/filedlg.h"
	"wx/os2/filedlg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\filedlgg.h
	"wx/listctrl.h"
	"wx/datetime.h"
	"wx/filefn.h"
	"wx/artprov.h"
	"wx/filedlg.h"
	"wx/generic/filectrlg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\listctrl.h
	"wx/defs.h"
	"wx/listbase.h"
	"wx/msw/listctrl.h"
	"wx/osx/listctrl.h"
	"wx/generic/listctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\listbase.h
	"wx/colour.h"
	"wx/font.h"
	"wx/gdicmn.h"
	"wx/event.h"
	"wx/control.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\listctrl.h
	"wx/textctrl.h"
	"wx/dynarray.h"
	"wx/vector.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\listctrl.h
	"wx/defs.h"
	"wx/generic/listctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\listctrl.h
	"wx/containr.h"
	"wx/scrolwin.h"
	"wx/textctrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\filectrlg.h
	"wx/containr.h"
	"wx/listctrl.h"
	"wx/filectrl.h"
	"wx/filename.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\filectrl.h
	"wx/defs.h"
	"wx/string.h"
	"wx/event.h"
	"wx/gtk/filectrl.h"
	"wx/generic/filectrlg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\filectrl.h
	"wx/control.h"
	"wx/filectrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\filedlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\motif\filedlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\filedlg.h
	"wx/gtk/filectrl.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\filedlg.h
	"wx/generic/filedlgg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\filedlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\filedlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\filedlg.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\mdi.h
	"wx/defs.h"
	"wx/frame.h"
	"wx/menu.h"
	"wx/generic/mdig.h"
	"wx/msw/mdi.h"
	"wx/gtk/mdi.h"
	"wx/gtk1/mdi.h"
	"wx/osx/mdi.h"
	"wx/cocoa/mdi.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\mdig.h
	"wx/panel.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\mdi.h
	"wx/frame.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\mdi.h
	"wx/frame.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk1\mdi.h
	"wx/frame.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\mdi.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cocoa\mdi.h
	"wx/frame.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\fs_mem.h
	"wx/defs.h"
	"wx/filesys.h"
	"wx/hashmap.h"
	"wx/bitmap.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_all.h
	"wx/xrc/xh_animatctrl.h"
	"wx/xrc/xh_bannerwindow.h"
	"wx/xrc/xh_bmp.h"
	"wx/xrc/xh_bmpbt.h"
	"wx/xrc/xh_bmpcbox.h"
	"wx/xrc/xh_bttn.h"
	"wx/xrc/xh_cald.h"
	"wx/xrc/xh_chckb.h"
	"wx/xrc/xh_chckl.h"
	"wx/xrc/xh_choic.h"
	"wx/xrc/xh_choicbk.h"
	"wx/xrc/xh_clrpicker.h"
	"wx/xrc/xh_cmdlinkbn.h"
	"wx/xrc/xh_collpane.h"
	"wx/xrc/xh_combo.h"
	"wx/xrc/xh_comboctrl.h"
	"wx/xrc/xh_datectrl.h"
	"wx/xrc/xh_dirpicker.h"
	"wx/xrc/xh_dlg.h"
	"wx/xrc/xh_editlbox.h"
	"wx/xrc/xh_filectrl.h"
	"wx/xrc/xh_filepicker.h"
	"wx/xrc/xh_fontpicker.h"
	"wx/xrc/xh_frame.h"
	"wx/xrc/xh_gauge.h"
	"wx/xrc/xh_gdctl.h"
	"wx/xrc/xh_grid.h"
	"wx/xrc/xh_html.h"
	"wx/xrc/xh_htmllbox.h"
	"wx/xrc/xh_hyperlink.h"
	"wx/xrc/xh_listb.h"
	"wx/xrc/xh_listc.h"
	"wx/xrc/xh_listbk.h"
	"wx/xrc/xh_mdi.h"
	"wx/xrc/xh_menu.h"
	"wx/xrc/xh_notbk.h"
	"wx/xrc/xh_odcombo.h"
	"wx/xrc/xh_panel.h"
	"wx/xrc/xh_propdlg.h"
	"wx/xrc/xh_radbt.h"
	"wx/xrc/xh_radbx.h"
	"wx/xrc/xh_scrol.h"
	"wx/xrc/xh_scwin.h"
	"wx/xrc/xh_simplebook.h"
	"wx/xrc/xh_sizer.h"
	"wx/xrc/xh_slidr.h"
	"wx/xrc/xh_spin.h"
	"wx/xrc/xh_split.h"
	"wx/xrc/xh_srchctrl.h"
	"wx/xrc/xh_statbar.h"
	"wx/xrc/xh_stbox.h"
	"wx/xrc/xh_stbmp.h"
	"wx/xrc/xh_sttxt.h"
	"wx/xrc/xh_stlin.h"
	"wx/xrc/xh_text.h"
	"wx/xrc/xh_tglbtn.h"
	"wx/xrc/xh_timectrl.h"
	"wx/xrc/xh_toolb.h"
	"wx/xrc/xh_toolbk.h"
	"wx/xrc/xh_tree.h"
	"wx/xrc/xh_treebk.h"
	"wx/xrc/xh_unkwn.h"
	"wx/xrc/xh_wizrd.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_animatctrl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_bannerwindow.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_bmp.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_bmpbt.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_bmpcbox.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_bttn.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_cald.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_chckb.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_chckl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_choic.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_choicbk.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_clrpicker.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_cmdlinkbn.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_collpane.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_combo.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_comboctrl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_datectrl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_dirpicker.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_dlg.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_editlbox.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_filectrl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_filepicker.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_fontpicker.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_frame.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_gauge.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_gdctl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_grid.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_html.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_htmllbox.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_hyperlink.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_listb.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_listc.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_listbk.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_mdi.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_menu.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_notbk.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_odcombo.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_panel.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_propdlg.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_radbt.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_radbx.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_scrol.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_scwin.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_simplebook.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_sizer.h
	"wx/xrc/xmlres.h"
	"wx/sizer.h"
	"wx/gbsizer.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gbsizer.h
	"wx/sizer.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_slidr.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_spin.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_split.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_srchctrl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_statbar.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_stbox.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_stbmp.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_sttxt.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_stlin.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_text.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_tglbtn.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_timectrl.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_toolb.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_toolbk.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_tree.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_treebk.h
	"wx/xrc/xmlres.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_unkwn.h
	"wx/xrc/xmlres.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\xrc\xh_wizrd.h
	"wx/xrc/xmlres.h"

1731586125 e:\tck.w_client_iot\control\transstatictext.h
	<wx/window.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>

1731583802 source:e:\tck.w_client_iot\control\bitmapbutton.cpp
	"BitmapButton.h"
	<wx/dcclient.h>
	<wx/dcmemory.h>
	<wx/file.h>
	<wx/msgdlg.h>
	"../TCK_WR_LANG.h"

1731583801 e:\tck.w_client_iot\control\bitmapbutton.h
	<wx/button.h>

1532588670 source:e:\tck.w_client_iot\control\mediaaudio.cpp
	"MediaAudio.h"
	<wx/msgdlg.h>
	"../TCK_WR_LANG.h"

1364276550 e:\tck.w_client_iot\control\mediaaudio.h
	<wx/mediactrl.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\mediactrl.h
	"wx/defs.h"
	"wx/control.h"
	"wx/uri.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\uri.h
	"wx/defs.h"
	"wx/object.h"
	"wx/string.h"
	"wx/arrstr.h"

1669883456 source:e:\tck.w_client_iot\control\transstaticbmp.cpp
	"TransStaticBmp.h"
	<wx/dcclient.h>
	<wx/xrc/xmlres.h>
	"../TCK_WR_LANG.h"

1664432296 e:\tck.w_client_iot\control\transstaticbmp.h
	<wx/window.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>
	<wx/timer.h>

1731586126 source:e:\tck.w_client_iot\control\transstatictext.cpp
	"TransStaticText.h"
	<wx/dcclient.h>
	"../TCK_WR_LANG.h"

1559031935 source:e:\tck.w_client_iot\control\transwin.cpp
	"TransWin.h"
	<wx/xrc/xmlres.h>
	<wx/dcclient.h>
	"../TCK_WR_LANG.h"

1419918432 e:\tck.w_client_iot\control\transwin.h
	<wx/frame.h>
	<wx/bitmap.h>

1693987204 e:\tck.w_client_iot\beltframe.h
	<wx/frame.h>
	<wx/panel.h>
	<wx/timer.h>
	"MainFrame.h"
	"Control/TransStaticText.h"
	"Control/TransStaticBmp.h"
	"BeltChart.h"
	"BeltCurve.h"
	"tcp_client_view.h"
	"BeltQueryFlaw.h"
	"BeltQueryJoint.h"
	"BeltQueryAlarm.h"
	"BeltReport.h"
	"BeltQueryTear.h"
	"RunningView.h"
	"BeltQueryTearAbn.h"
	"ZXQueryFlaw.h"
	"ZXReport.h"
	"ZXStatDialog.h"
	"ZXQueryAlarm.h"

1747113114 source:e:\tck.w_client_iot\beltframe.cpp
	"BeltFrame.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/msgdlg.h>
	"TCK_W_Client_IoTApp.h"
	"TCK_WR_LANG.h"
	<wx/mstream.h>
	"Validity.h"

1660628106 e:\tck.w_client_iot\beltchartflawlev.h
	<wx/panel.h>
	<wx/bitmap.h>

1660628130 source:e:\tck.w_client_iot\beltchartflawlev.cpp
	"BeltChartFlawLev.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	"TCK_WR_LANG.h"

1704952237 e:\tck.w_client_iot\beltcurve.h
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	"yblib.h"
	"TCK_W_Client_IoTApp.h"
	<vector>

********** source:e:\tck.w_client_iot\beltcurve.cpp
	"BeltCurve.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	<wx/xrc/xmlres.h>
	<vector>
	"TCK_WR_LANG.h"

********** e:\tck.w_client_iot\beltchart.h
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	<vector>
	"yblib.h"

********** source:e:\tck.w_client_iot\beltchart.cpp
	"BeltChart.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	"TCK_WR_LANG.h"

********** source:e:\tck.w_client_iot\accountdialog.cpp
	"AccountDialog.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	<wx/msgdlg.h>
	<wx/textdlg.h>
	"TCK_WR_LANG.h"

********** e:\tck.w_client_iot\accountdialog.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/grid.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<string>
	<map>
	<vector>
	"MainFrame.h"

********** e:\tck.w_client_iot\beltqueryflaw.h
	<wx/bmpbuttn.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/treectrl.h>
	<wx/panel.h>
	<wx/scrolwin.h>
	<wx/statbmp.h>
	"yblib.h"
	<string>
	<list>
	<map>
	"BeltChart.h"
	"tcp_client_srcdata.h"
	"wxCurve.h"
	"analogChart.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\treectrl.h
	"wx/defs.h"
	"wx/control.h"
	"wx/treebase.h"
	"wx/textctrl.h"
	"wx/generic/treectlg.h"
	"wx/msw/treectrl.h"
	"wx/generic/treectlg.h"
	"wx/generic/treectlg.h"
	"wx/generic/treectlg.h"
	"wx/generic/treectlg.h"
	"wx/generic/treectlg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\treebase.h
	"wx/defs.h"
	"wx/window.h"
	"wx/event.h"
	"wx/dynarray.h"
	"wx/itemid.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\itemid.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\treectlg.h
	"wx/scrolwin.h"
	"wx/pen.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\treectrl.h
	"wx/textctrl.h"
	"wx/dynarray.h"
	"wx/treebase.h"
	"wx/hashmap.h"

1747273806 source:e:\tck.w_client_iot\beltqueryflaw.cpp
	"BeltQueryFlaw.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/imaglist.h>
	<wx/msgdlg.h>
	"tcp_client_web.h"
	"TCK_WR_LANG.h"

1689756928 e:\tck.w_client_iot\tcp_client_srcdata.h
	"yblib.h"
	<string>
	<vector>
	<wx/event.h>

1739253304 source:e:\tck.w_client_iot\tcp_client_srcdata.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<dirent.h>
	<direct.h>
	"tcp_client_srcdata.h"
	"DebugWin.h"
	<wx/event.h>

1704096997 e:\tck.w_client_iot\include\wxcurve.h
	<wx/wx.h>
	<wx/panel.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>
	<vector>
	"yblib.h"

1747027335 e:\tck.w_client_iot\beltqueryjoint.h
	<wx/button.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/statbox.h>
	<wx/stattext.h>
	"tcp_client_srcdata.h"
	"wxCurve.h"
	<vector>
	"StatChart.h"

1747213541 source:e:\tck.w_client_iot\beltqueryjoint.cpp
	"BeltQueryJoint.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"
	"tcp_client_web.h"
	<map>
	"BeltJointCurve.h"
	"BeltYBRedoSelDate.h"

1670816122 source:e:\tck.w_client_iot\beltjointcurve.cpp
	"BeltJointCurve.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/file.h>
	"TCK_WR_LANG.h"

1661670726 e:\tck.w_client_iot\beltjointcurve.h
	<wx/dialog.h>
	<wx/statbox.h>
	"wxCurve.h"

1662370297 e:\tck.w_client_iot\beltqueryalarm.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/stattext.h>
	"yblib.h"
	<string>

1693987205 source:e:\tck.w_client_iot\beltqueryalarm.cpp
	"BeltQueryAlarm.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"
	"tcp_client_web.h"
	<wx/msgdlg.h>

1662360438 e:\tck.w_client_iot\beltybredoseldate.h
	<wx/button.h>
	<wx/calctrl.h>
	<wx/dialog.h>
	<wx/stattext.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\calctrl.h
	"wx/defs.h"
	"wx/dateevt.h"
	"wx/colour.h"
	"wx/font.h"
	"wx/control.h"
	"wx/gtk/calctrl.h"
	"wx/msw/calctrl.h"
	"wx/generic/calctrlg.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dateevt.h
	"wx/event.h"
	"wx/datetime.h"
	"wx/window.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\gtk\calctrl.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\calctrl.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\calctrlg.h
	"wx/control.h"
	"wx/dcclient.h"

1668155565 source:e:\tck.w_client_iot\beltybredoseldate.cpp
	"BeltYBRedoSelDate.h"
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"

1747273807 source:e:\tck.w_client_iot\beltreport.cpp
	"BeltReport.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/file.h>
	"TCK_WR_LANG.h"
	<wx/dir.h>
	"StatChart.h"
	"analogChart.h"

1668047450 e:\tck.w_client_iot\beltreport.h
	<wx/button.h>
	<wx/checkbox.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/treectrl.h>
	<wx/print.h>
	"tcp_client_web.h"
	<string>
	"BeltQueryFlaw.h"
	"BeltQueryJoint.h"
	"tcp_client_srcdata.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\dir.h
	"wx/longlong.h"
	"wx/string.h"
	"wx/filefn.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\print.h
	"wx/defs.h"
	"wx/msw/printwin.h"
	"wx/osx/printmac.h"
	"wx/os2/printos2.h"
	"wx/generic/printps.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\printwin.h
	"wx/prntbase.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\prntbase.h
	"wx/defs.h"
	"wx/event.h"
	"wx/cmndata.h"
	"wx/panel.h"
	"wx/scrolwin.h"
	"wx/dialog.h"
	"wx/frame.h"
	"wx/dc.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\cmndata.h
	"wx/defs.h"
	"wx/gdicmn.h"
	"wx/stream.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\printmac.h
	"wx/prntbase.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\printos2.h
	"wx/prntbase.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\generic\printps.h
	"wx/prntbase.h"

1665628097 e:\tck.w_client_iot\beltquerytear.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/scrolwin.h>
	<wx/stattext.h>
	<vector>
	"yblib.h"
	"tcp_client_web.h"
	"tcp_client_srcdata.h"
	"BeltTearReport.h"

1728526227 source:e:\tck.w_client_iot\beltquerytear.cpp
	"BeltQueryTear.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dir.h>
	<wx/statbmp.h>
	<wx/msgdlg.h>
	"TCK_WR_LANG.h"
	"PhotoView.h"

1663652111 e:\tck.w_client_iot\validity.h
	<wx/button.h>
	<wx/checkbox.h>
	<wx/dialog.h>
	<wx/panel.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<string>

1663725170 source:e:\tck.w_client_iot\validity.cpp
	"Validity.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	<wx/msgdlg.h>
	"tcp_client_web.h"

1663912522 source:e:\tck.w_client_iot\photoview.cpp
	"PhotoView.h"
	"TCK_WR_LANG.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcclient.h>
	<wx/dcmemory.h>

1663912520 e:\tck.w_client_iot\photoview.h
	<wx/dialog.h>
	<wx/bitmap.h>

1690541331 e:\tck.w_client_iot\runningview.h
	<wx/panel.h>
	<wx/bitmap.h>

1690640104 source:e:\tck.w_client_iot\runningview.cpp
	"RunningView.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	"TCK_WR_LANG.h"

1665552130 e:\tck.w_client_iot\beltquerytearabn.h
	<wx/dialog.h>
	<wx/grid.h>
	<wx/stattext.h>
	"yblib.h"

1690452008 source:e:\tck.w_client_iot\beltquerytearabn.cpp
	"BeltQueryTearAbn.h"
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"

1665631888 e:\tck.w_client_iot\belttearreport.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/print.h>
	<string>
	<vector>
	"tcp_client_web.h"
	"tcp_client_srcdata.h"

1669793921 source:e:\tck.w_client_iot\belttearreport.cpp
	"BeltTearReport.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/file.h>
	<wx/dir.h>
	<wx/dcmemory.h>
	"TCK_WR_LANG.h"

1704945604 e:\tck.w_client_iot\zxqueryflaw.h
	<wx/bmpbuttn.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/treectrl.h>
	"yblib.h"
	<string>
	<list>
	<map>
	"BeltChart.h"
	"tcp_client_srcdata.h"
	"wxCurve.h"

1704945605 e:\tck.w_client_iot\zxreport.h
	<wx/button.h>
	<wx/checkbox.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/treectrl.h>
	<wx/print.h>
	"tcp_client_web.h"
	<string>
	"ZXQueryFlaw.h"
	"tcp_client_srcdata.h"

1747028669 e:\tck.w_client_iot\zxstatdialog.h
	<wx/dialog.h>
	<wx/statbox.h>
	"yblib.h"
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	<wx/datetime.h>
	<vector>
	"StatChart.h"

1739154602 source:e:\tck.w_client_iot\zxqueryflaw.cpp
	"ZXQueryFlaw.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/imaglist.h>
	<wx/msgdlg.h>
	"tcp_client_web.h"
	"TCK_WR_LANG.h"

1747028670 source:e:\tck.w_client_iot\zxstatdialog.cpp
	"ZXStatDialog.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcclient.h>
	"tcp_client_web.h"

1707285311 source:e:\tck.w_client_iot\zxreport.cpp
	"ZXReport.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/file.h>
	"TCK_WR_LANG.h"
	<wx/dir.h>

1737608500 e:\tck.w_client_iot\zxqueryalarm.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/stattext.h>
	"yblib.h"
	<string>
	<map>

1737608593 source:e:\tck.w_client_iot\zxqueryalarm.cpp
	"ZXQueryAlarm.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"
	"tcp_client_web.h"
	<wx/msgdlg.h>

1747273802 e:\tck.w_client_iot\statchart.h
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	<vector>

********** source:e:\tck.w_client_iot\statchart.cpp
	"StatChart.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	"TCK_WR_LANG.h"

********** e:\tck.w_client_iot\analogchart.h
	<wx/wx.h>
	<wx/panel.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>
	<vector>

********** source:e:\tck.w_client_iot\analogchart.cpp
	"analogChart.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcclient.h>
	<sys/time.h>
	<unistd.h>
	<pthread.h>
	<math.h>

********** source:d:\code\client_branches\add_joint_capture\accountdialog.cpp
	"AccountDialog.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	<wx/msgdlg.h>
	<wx/textdlg.h>
	"TCK_WR_LANG.h"

********** d:\code\client_branches\add_joint_capture\accountdialog.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/grid.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<string>
	<map>
	<vector>
	"MainFrame.h"

********** d:\code\client_branches\add_joint_capture\mainframe.h
	<wx/frame.h>
	<wx/panel.h>
	<wx/scrolwin.h>
	<wx/timer.h>
	<wx/sizer.h>
	<wx/stattext.h>
	<wx/statbmp.h>
	"tcp_client_web.h"
	<map>
	"Control/TransStaticText.h"
	"Control/TransStaticBmp.h"
	"Control/BitmapButton.h"

********** d:\code\client_branches\add_joint_capture\tcp_client_web.h
	"yblib.h"
	<string>

********** d:\code\client_branches\add_joint_capture\include\yblib.h

********** d:\code\client_branches\add_joint_capture\control\transstatictext.h
	<wx/window.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>

********** d:\code\client_branches\add_joint_capture\control\transstaticbmp.h
	<wx/window.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>
	<wx/timer.h>

********** d:\code\client_branches\add_joint_capture\control\bitmapbutton.h
	<wx/button.h>

********** d:\code\client_branches\add_joint_capture\tck_wr_lang.h
	<wx/strconv.h>

1753174047 source:d:\code\client_branches\add_joint_capture\analogchart.cpp
	"analogChart.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcclient.h>
	<sys/time.h>
	<unistd.h>
	<pthread.h>
	<math.h>

1753174050 d:\code\client_branches\add_joint_capture\analogchart.h
	<wx/wx.h>
	<wx/panel.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>
	<vector>

1753174054 source:d:\code\client_branches\add_joint_capture\beltchart.cpp
	"BeltChart.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	"TCK_WR_LANG.h"

1753174057 d:\code\client_branches\add_joint_capture\beltchart.h
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	<vector>
	"yblib.h"

********** source:d:\code\client_branches\add_joint_capture\beltcurve.cpp
	"BeltCurve.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	<wx/xrc/xmlres.h>
	<vector>
	"TCK_WR_LANG.h"

********** d:\code\client_branches\add_joint_capture\beltcurve.h
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	"yblib.h"
	"TCK_W_Client_IoTApp.h"
	<vector>

********** d:\code\client_branches\add_joint_capture\tck_w_client_iotapp.h
	<wx/app.h>
	<wx/snglinst.h>

1753687636 source:d:\code\client_branches\add_joint_capture\beltframe.cpp
	"BeltFrame.h"
	"Logger.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/msgdlg.h>
	"TCK_W_Client_IoTApp.h"
	"TCK_WR_LANG.h"
	<wx/mstream.h>
	"Validity.h"

1753404985 d:\code\client_branches\add_joint_capture\beltframe.h
	<wx/frame.h>
	<wx/panel.h>
	<wx/timer.h>
	"MainFrame.h"
	"Control/TransStaticText.h"
	"Control/TransStaticBmp.h"
	"BeltChart.h"
	"BeltCurve.h"
	"tcp_client_view.h"
	"BeltQueryFlaw.h"
	"BeltQueryJoint.h"
	"BeltQueryAlarm.h"
	"BeltReport.h"
	"BeltQueryTear.h"
	"RunningView.h"
	"BeltQueryTearAbn.h"
	"ZXQueryFlaw.h"
	"ZXReport.h"
	"ZXStatDialog.h"
	"ZXQueryAlarm.h"
	"JointCaptureController.h"
	"CaptureConfig.h"
	"CircleManager.h"
	<thread>

********** d:\code\client_branches\add_joint_capture\tcp_client_view.h
	"yblib.h"
	<string>
	<wx/event.h>

********** d:\code\client_branches\add_joint_capture\beltqueryflaw.h
	<wx/bmpbuttn.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/treectrl.h>
	<wx/panel.h>
	<wx/scrolwin.h>
	<wx/statbmp.h>
	"yblib.h"
	<string>
	<list>
	<map>
	"BeltChart.h"
	"tcp_client_srcdata.h"
	"wxCurve.h"
	"analogChart.h"

********** d:\code\client_branches\add_joint_capture\tcp_client_srcdata.h
	"yblib.h"
	<string>
	<vector>
	<wx/event.h>

********** d:\code\client_branches\add_joint_capture\include\wxcurve.h
	<wx/wx.h>
	<wx/panel.h>
	<wx/dcmemory.h>
	<wx/bitmap.h>
	<vector>
	"yblib.h"

1753251045 d:\code\client_branches\add_joint_capture\beltqueryjoint.h
	<wx/button.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/statbox.h>
	<wx/stattext.h>
	"tcp_client_srcdata.h"
	"wxCurve.h"
	<vector>
	"StatChart.h"
	"PhotoStorageManager.h"

********** d:\code\client_branches\add_joint_capture\statchart.h
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	<vector>

********** d:\code\client_branches\add_joint_capture\beltqueryalarm.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/stattext.h>
	"yblib.h"
	<string>

********** d:\code\client_branches\add_joint_capture\beltreport.h
	<wx/button.h>
	<wx/checkbox.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/treectrl.h>
	<wx/print.h>
	"tcp_client_web.h"
	<string>
	"BeltQueryFlaw.h"
	"BeltQueryJoint.h"
	"tcp_client_srcdata.h"

********** d:\code\client_branches\add_joint_capture\beltquerytear.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/scrolwin.h>
	<wx/stattext.h>
	<vector>
	"yblib.h"
	"tcp_client_web.h"
	"tcp_client_srcdata.h"
	"BeltTearReport.h"

********** d:\code\client_branches\add_joint_capture\belttearreport.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/print.h>
	<string>
	<vector>
	"tcp_client_web.h"
	"tcp_client_srcdata.h"

********** d:\code\client_branches\add_joint_capture\runningview.h
	<wx/panel.h>
	<wx/bitmap.h>

********** d:\code\client_branches\add_joint_capture\beltquerytearabn.h
	<wx/dialog.h>
	<wx/grid.h>
	<wx/stattext.h>
	"yblib.h"

********** d:\code\client_branches\add_joint_capture\zxqueryflaw.h
	<wx/bmpbuttn.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/treectrl.h>
	"yblib.h"
	<string>
	<list>
	<map>
	"BeltChart.h"
	"tcp_client_srcdata.h"
	"wxCurve.h"

********** d:\code\client_branches\add_joint_capture\zxreport.h
	<wx/button.h>
	<wx/checkbox.h>
	<wx/choice.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/statbox.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/treectrl.h>
	<wx/print.h>
	"tcp_client_web.h"
	<string>
	"ZXQueryFlaw.h"
	"tcp_client_srcdata.h"

********** d:\code\client_branches\add_joint_capture\zxstatdialog.h
	<wx/dialog.h>
	<wx/statbox.h>
	"yblib.h"
	<wx/panel.h>
	<wx/bitmap.h>
	<wx/dcmemory.h>
	<wx/datetime.h>
	<vector>
	"StatChart.h"

********** d:\code\client_branches\add_joint_capture\zxqueryalarm.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/gauge.h>
	<wx/grid.h>
	<wx/stattext.h>
	"yblib.h"
	<string>
	<map>

********** d:\code\client_branches\add_joint_capture\validity.h
	<wx/button.h>
	<wx/checkbox.h>
	<wx/dialog.h>
	<wx/panel.h>
	<wx/stattext.h>
	<wx/textctrl.h>
	<string>

********** source:d:\code\client_branches\add_joint_capture\beltjointcurve.cpp
	"BeltJointCurve.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/file.h>
	"TCK_WR_LANG.h"

********** d:\code\client_branches\add_joint_capture\beltjointcurve.h
	<wx/dialog.h>
	<wx/statbox.h>
	"wxCurve.h"

********** source:d:\code\client_branches\add_joint_capture\beltqueryalarm.cpp
	"BeltQueryAlarm.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"
	"tcp_client_web.h"
	<wx/msgdlg.h>

********** source:d:\code\client_branches\add_joint_capture\beltqueryflaw.cpp
	"BeltQueryFlaw.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/imaglist.h>
	<wx/msgdlg.h>
	"tcp_client_web.h"
	"TCK_WR_LANG.h"

1753251261 source:d:\code\client_branches\add_joint_capture\beltqueryjoint.cpp
	"BeltQueryJoint.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"
	"Logger.h"
	"tcp_client_web.h"
	<map>
	"BeltJointCurve.h"
	"BeltYBRedoSelDate.h"
	"PhotoViewer.h"

********** d:\code\client_branches\add_joint_capture\beltybredoseldate.h
	<wx/button.h>
	<wx/calctrl.h>
	<wx/dialog.h>
	<wx/stattext.h>

********** source:d:\code\client_branches\add_joint_capture\beltquerytear.cpp
	"BeltQueryTear.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dir.h>
	<wx/statbmp.h>
	<wx/msgdlg.h>
	"TCK_WR_LANG.h"
	"PhotoView.h"

********** d:\code\client_branches\add_joint_capture\photoview.h
	<wx/dialog.h>
	<wx/bitmap.h>

********** source:d:\code\client_branches\add_joint_capture\beltquerytearabn.cpp
	"BeltQueryTearAbn.h"
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\beltreport.cpp
	"BeltReport.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/file.h>
	"TCK_WR_LANG.h"
	<wx/dir.h>
	"StatChart.h"
	"analogChart.h"

********** source:d:\code\client_branches\add_joint_capture\belttearreport.cpp
	"BeltTearReport.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/file.h>
	<wx/dir.h>
	<wx/dcmemory.h>
	"TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\beltybredoseldate.cpp
	"BeltYBRedoSelDate.h"
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\control\bitmapbutton.cpp
	"BitmapButton.h"
	<wx/dcclient.h>
	<wx/dcmemory.h>
	<wx/file.h>
	<wx/msgdlg.h>
	"../TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\control\mediaaudio.cpp
	"MediaAudio.h"
	<wx/msgdlg.h>
	"../TCK_WR_LANG.h"

********** d:\code\client_branches\add_joint_capture\control\mediaaudio.h
	<wx/mediactrl.h>

********** source:d:\code\client_branches\add_joint_capture\control\transstaticbmp.cpp
	"TransStaticBmp.h"
	<wx/dcclient.h>
	<wx/xrc/xmlres.h>
	"../TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\control\transstatictext.cpp
	"TransStaticText.h"
	<wx/dcclient.h>
	"../TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\control\transwin.cpp
	"TransWin.h"
	<wx/xrc/xmlres.h>
	<wx/dcclient.h>
	"../TCK_WR_LANG.h"

********** d:\code\client_branches\add_joint_capture\control\transwin.h
	<wx/frame.h>
	<wx/bitmap.h>

********** source:d:\code\client_branches\add_joint_capture\debugwin.cpp
	"DebugWin.h"
	<wx/intl.h>
	<wx/string.h>

********** d:\code\client_branches\add_joint_capture\debugwin.h
	<wx/dialog.h>
	<wx/textctrl.h>
	"yblib.h"

********** source:d:\code\client_branches\add_joint_capture\mainframe.cpp
	"MainFrame.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	<wx/msgdlg.h>
	"DebugWin.h"
	"TCK_W_Client_IoTMain.h"
	"BeltFrame.h"
	"TCK_WR_LANG.h"
	"Validity.h"
	<wx/translation.h>
	"AccountDialog.h"

********** d:\code\client_branches\add_joint_capture\tck_w_client_iotmain.h
	<wx/button.h>
	<wx/dialog.h>
	<wx/stattext.h>
	<wx/textctrl.h>

********** source:d:\code\client_branches\add_joint_capture\notewin.cpp
	"NoteWin.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	<wx/snglinst.h>
	"TCK_WR_LANG.h"

********** d:\code\client_branches\add_joint_capture\notewin.h
	<wx/stattext.h>
	<wx/textctrl.h>
	<wx/checkbox.h>
	<wx/panel.h>
	<wx/button.h>
	<wx/dialog.h>

********** source:d:\code\client_branches\add_joint_capture\photoview.cpp
	"PhotoView.h"
	"TCK_WR_LANG.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcclient.h>
	<wx/dcmemory.h>

********** source:d:\code\client_branches\add_joint_capture\resource.rc
	"wx/msw/wx.rc"

********** source:d:\code\client_branches\add_joint_capture\runningview.cpp
	"RunningView.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	"TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\statchart.cpp
	"StatChart.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/dcmemory.h>
	<wx/dcclient.h>
	"TCK_WR_LANG.h"

1753235321 source:d:\code\client_branches\add_joint_capture\tck_w_client_iotapp.cpp
	"TCK_W_Client_IoTApp.h"
	"TCK_W_Client_IoTMain.h"
	<wx/image.h>
	"DebugWin.h"
	"tcp_client_web.h"
	"NoteWin.h"
	"MainFrame.h"
	<wx/msgdlg.h>
	<wx/xrc/xmlres.h>
	"JPG/Resources.h"
	"TCK_WR_LANG.h"
	"Logger.h"
	<direct.h>
	<TlHelp32.h>
	<iphlpapi.h>
	<wx/thread.h>
	"tcp_client_view.h"
	<unistd.h>
	<sys/time.h>

1753174007 d:\code\client_branches\add_joint_capture\jpg\resources.h
	<wx/wxprec.h>
	<wx/filesys.h>
	<wx/fs_mem.h>
	<wx/xrc/xmlres.h>
	<wx/xrc/xh_all.h>

********** source:d:\code\client_branches\add_joint_capture\tck_w_client_iotmain.cpp
	"TCK_W_Client_IoTMain.h"
	<wx/msgdlg.h>
	<wx/xrc/xmlres.h>
	<wx/intl.h>
	<wx/string.h>
	"tcp_client_web.h"

********** source:d:\code\client_branches\add_joint_capture\tcp_client_srcdata.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<dirent.h>
	<direct.h>
	"tcp_client_srcdata.h"
	"DebugWin.h"
	<wx/event.h>

********** source:d:\code\client_branches\add_joint_capture\tcp_client_view.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client_view.h"
	"DebugWin.h"

********** source:d:\code\client_branches\add_joint_capture\tcp_client_web.cpp
	<stdio.h>
	<stdlib.h>
	<time.h>
	<unistd.h>
	<cstring>
	"tcp_client_web.h"
	"DebugWin.h"
	<wx/event.h>

********** source:d:\code\client_branches\add_joint_capture\validity.cpp
	"Validity.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	<wx/msgdlg.h>
	"tcp_client_web.h"

********** source:d:\code\client_branches\add_joint_capture\zxqueryalarm.cpp
	"ZXQueryAlarm.h"
	<wx/font.h>
	<wx/intl.h>
	<wx/string.h>
	"TCK_WR_LANG.h"
	"tcp_client_web.h"
	<wx/msgdlg.h>

********** source:d:\code\client_branches\add_joint_capture\zxqueryflaw.cpp
	"ZXQueryFlaw.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/imaglist.h>
	<wx/msgdlg.h>
	"tcp_client_web.h"
	"TCK_WR_LANG.h"

********** source:d:\code\client_branches\add_joint_capture\zxreport.cpp
	"ZXReport.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/xrc/xmlres.h>
	<wx/file.h>
	"TCK_WR_LANG.h"
	<wx/dir.h>

********** source:d:\code\client_branches\add_joint_capture\zxstatdialog.cpp
	"ZXStatDialog.h"
	<wx/intl.h>
	<wx/string.h>
	<wx/dcclient.h>
	"tcp_client_web.h"

1753241236 d:\code\client_branches\add_joint_capture\photostoragemanager.h
	<wx/wx.h>
	<wx/datetime.h>

1753686364 d:\code\client_branches\add_joint_capture\jointcapturecontroller.h
	<wx/wx.h>
	<wx/timer.h>
	<wx/thread.h>
	"CameraManager.h"
	"PhotoStorageManager.h"
	"CaptureConfig.h"

1753175197 d:\code\client_branches\add_joint_capture\cameramanager.h
	<wx/wx.h>
	<wx/thread.h>
	"include/HCNetSDK.h"

********** d:\code\client_branches\add_joint_capture\include\hcnetsdk.h
	<winsock2.h>
	<windows.h>

1753681779 d:\code\client_branches\add_joint_capture\captureconfig.h
	<wx/wx.h>
	<wx/fileconf.h>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\fileconf.h
	"wx/defs.h"
	"wx/textfile.h"
	"wx/string.h"
	"wx/confbase.h"
	"wx/filename.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\textfile.h
	"wx/defs.h"
	"wx/textbuf.h"
	"wx/file.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\textbuf.h
	"wx/defs.h"
	"wx/arrstr.h"
	"wx/convauto.h"
	"wx/string.h"
	"wx/dynarray.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\confbase.h
	"wx/defs.h"
	"wx/string.h"
	"wx/object.h"
	"wx/base64.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\base64.h
	"wx/string.h"
	"wx/buffer.h"

1753254312 d:\code\client_branches\add_joint_capture\photoviewer.h
	<wx/wx.h>
	<wx/dialog.h>
	<wx/panel.h>
	<wx/sizer.h>
	<wx/button.h>
	<wx/stattext.h>
	<wx/scrolwin.h>

1753235476 source:d:\code\client_branches\add_joint_capture\cameramanager.cpp
	"CameraManager.h"
	"Logger.h"
	<wx/filename.h>
	<wx/dir.h>

1753681705 source:d:\code\client_branches\add_joint_capture\captureconfig.cpp
	"CaptureConfig.h"
	<wx/filename.h>
	<wx/stdpaths.h>
	"Logger.h"

1753686152 source:d:\code\client_branches\add_joint_capture\jointcapturecontroller.cpp
	"JointCaptureController.h"
	<wx/log.h>
	"Logger.h"

1753241699 source:d:\code\client_branches\add_joint_capture\photostoragemanager.cpp
	"PhotoStorageManager.h"
	<wx/filename.h>
	<wx/dir.h>
	<wx/filefn.h>
	<wx/regex.h>
	"Logger.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\regex.h
	"wx/defs.h"
	"wx/string.h"

1753254408 source:d:\code\client_branches\add_joint_capture\photoviewer.cpp
	"PhotoViewer.h"
	<wx/image.h>
	<wx/filename.h>
	"Logger.h"

1753234005 d:\code\client_branches\add_joint_capture\logger.h
	<fstream>
	<mutex>
	<string>

1753249217 source:d:\code\client_branches\add_joint_capture\logger.cpp
	"Logger.h"
	<iostream>
	<iomanip>
	<sstream>
	<chrono>

1753262790 d:\code\client_branches\add_joint_capture\circlemanager.h
	<wx/string.h>

1753262778 source:d:\code\client_branches\add_joint_capture\circlemanager.cpp
	"CircleManager.h"
	"Logger.h"
	<cmath>

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\stdpaths.h
	"wx/defs.h"
	"wx/string.h"
	"wx/filefn.h"
	"wx/msw/stdpaths.h"
	"wx/osx/core/stdpaths.h"
	"wx/os2/stdpaths.h"
	"wx/unix/stdpaths.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\msw\stdpaths.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\osx\core\stdpaths.h
	"wx/unix/stdpaths.h"

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\unix\stdpaths.h

********** d:\codeblocks-17.12\wxwidgets-3.0.2\include\wx\os2\stdpaths.h

