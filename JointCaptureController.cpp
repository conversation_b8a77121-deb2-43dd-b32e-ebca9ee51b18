#include "JointCaptureController.h"
#include <wx/log.h>
#include "Logger.h"

// 事件表
wxBEGIN_EVENT_TABLE(JointCaptureController, wxEvtHandler)
    EVT_TIMER(ID_CAPTURE_TIMER, JointCaptureController::OnCaptureTimer)
wxEND_EVENT_TABLE()

JointCaptureController::JointCaptureController()
    : m_cameraDistance(2.0f)
    , m_autoCaptureEnabled(true)
    , m_captureTimer(nullptr)
{
    m_captureTimer = new wxTimer(this, ID_CAPTURE_TIMER);

    // 加载配置文件
    if (!m_config.LoadConfig())
    {
        LogWarning("加载配置文件失败，使用默认配置");
    }
    else
    {
        // 从配置文件更新摄像头距离
        m_cameraDistance = m_config.GetCameraDistance();
        m_autoCaptureEnabled = m_config.IsAutoCaptureEnabled();
        LogMessage("已加载配置: 摄像头距离=" + std::to_string(m_cameraDistance) + "米, 自动拍照=" +
                   (m_autoCaptureEnabled ? "启用" : "禁用"));
    }
}

JointCaptureController::~JointCaptureController()
{
    if (m_captureTimer)
    {
        m_captureTimer->Stop();
        delete m_captureTimer;
        m_captureTimer = nullptr;
    }
}

bool JointCaptureController::Initialize(const wxString& cameraIP, int cameraPort,
                                       const wxString& username,
                                       const wxString& password)
{
    // 初始化摄像头管理器
    if (!m_cameraManager.Initialize(cameraIP, cameraPort, username, password))
    {
        SetError("初始化摄像头失败: " + m_cameraManager.GetLastError());
        return false;
    }

    // 设置图片存储根目录
    m_photoStorageManager.SetRootPath("photos");

    return true;
}

void JointCaptureController::OnJointDetected(const JointInfo& jointInfo)
{
    if (!m_autoCaptureEnabled)
    {
        LogMessage("自动拍照已禁用，跳过接头 " + jointInfo.jointNumber.ToStdString());
        return;
    }

    if (!m_cameraManager.IsConnected())
    {
        SetError("摄像头未连接，无法拍照");
        return;
    }

    LogMessage("检测到接头: " + jointInfo.jointNumber.ToStdString() +
               ", 位置: " + std::to_string(jointInfo.startPos) + "-" + std::to_string(jointInfo.endPos) + "米" +
               ", 真实长度: " + std::to_string(jointInfo.realLen) + "米" +
               ", 速度: " + std::to_string(jointInfo.beltSpeed) + "米/秒");

    // 计算拍照延迟
    int delayMs = CalculateCaptureDelay(jointInfo);
    
    if (delayMs <= 0)
    {
        // 立即拍照
        ExecuteCapture(jointInfo);
    }
    else
    {
        // 延迟拍照
        ScheduleCapture(jointInfo, delayMs);
    }
}

void JointCaptureController::SetCurrentCircle(int circle)
{
    m_photoStorageManager.SetCurrentCircle(circle);
    LogMessage("设置当前圈数: " + std::to_string(circle));
}

void JointCaptureController::StartNewCircle()
{
    m_photoStorageManager.IncrementCircle();
    LogMessage("开始新的一圈，圈数: " + std::to_string(m_photoStorageManager.GetCurrentCircle()));
}

float JointCaptureController::CalculateOptimalCameraDistance(float jointLength)
{
    // 最佳安装距离 = 接头长度/2 + 安全缓冲距离
    const float SAFETY_BUFFER = 0.5f; // 安全缓冲距离(米)
    return jointLength / 2.0f + SAFETY_BUFFER;
}

int JointCaptureController::CalculateCaptureDelay(const JointInfo& jointInfo)
{
    // 参数验证：皮带速度
    if (jointInfo.beltSpeed <= 0)
    {
        LogError("皮带速度无效: " + std::to_string(jointInfo.beltSpeed) + "米/秒，无法计算延迟时间");
        return 0; // 立即拍照
    }

    // 参数验证：皮带速度合理性检查（通常在0.1-10米/秒范围内）
    if (jointInfo.beltSpeed < 0.1f || jointInfo.beltSpeed > 10.0f)
    {
        LogWarning("皮带速度(" + std::to_string(jointInfo.beltSpeed) + "米/秒)超出正常范围[0.1-10.0]");
    }

    // 使用真实接头长度（realLen字段）
    float jointLength = jointInfo.realLen;
    if (jointLength <= 0)
    {
        LogError("接头真实长度无效: " + std::to_string(jointLength) + "米，使用默认长度2.0米");
        jointLength = 2.0f; // 使用默认长度
    }

    // 参数验证：接头长度合理性检查（通常在0.8-3米范围内）
    if (jointLength < 0.8f || jointLength > 3.0f)
    {
        LogWarning("接头长度(" + std::to_string(jointLength) + "米)超出正常范围[0.8-3.0]");
    }

    // 参数验证：摄像头安装距离
    float actualDistance = m_cameraDistance;
    if (actualDistance <= 0)
    {
        LogError("摄像头安装距离无效: " + std::to_string(actualDistance) + "米，使用默认距离2.0米");
        actualDistance = 2.0f;
    }

    // 参数验证：摄像头距离合理性检查（通常在1-5米范围内）
    if (actualDistance < 1.0f || actualDistance > 5.0f)
    {
        LogWarning("摄像头安装距离(" + std::to_string(actualDistance) + "米)超出正常范围[1.0-5.0]");
    }

    // 获取安全冗余距离配置（默认1.0米）
    float safetyRedundancy = m_config.GetSafetyBuffer();
    if (safetyRedundancy < 0)
    {
        LogWarning("安全冗余距离配置无效: " + std::to_string(safetyRedundancy) + "米，使用默认值1.0米");
        safetyRedundancy = 1.0f;
    }

    // 检查摄像头距离是否足够（必须大于安全冗余距离）
    if (actualDistance <= safetyRedundancy)
    {
        LogError("摄像头安装距离(" + std::to_string(actualDistance) + "米)小于等于安全冗余距离(" +
                 std::to_string(safetyRedundancy) + "米)，立即拍照");
        return 0;
    }

    // 计算最佳摄像头距离（用于参考）
    float optimalDistance = CalculateOptimalCameraDistance(jointLength);

    // 如果实际安装距离与最佳距离差异较大，记录警告
    if (abs(actualDistance - optimalDistance) > 0.5f)
    {
        LogWarning("摄像头安装距离(" + std::to_string(actualDistance) + "米)与最佳距离(" +
                   std::to_string(optimalDistance) + "米)差异较大，可能影响拍照效果");
    }

    // 修正的延迟计算公式：延迟时间 = (摄像头与MRT设备的水平距离 - 安全冗余距离 - 接头真实长度) / 皮带速度
    float effectiveDistance = actualDistance - safetyRedundancy - jointLength;
    float delaySeconds = effectiveDistance / jointInfo.beltSpeed;

    // 确保延迟时间不为负数
    if (delaySeconds < 0)
    {
        LogWarning("计算得到负延迟时间(" + std::to_string(delaySeconds) + "秒)，立即拍照");
        return 0;
    }

    int delayMs = (int)(delaySeconds * 1000);

    // 详细的日志输出
    LogMessage("拍照延迟计算详情:");
    LogMessage("  接头编号: " + jointInfo.jointNumber.ToStdString());
    LogMessage("  接头真实长度: " + std::to_string(jointLength) + "米");
    LogMessage("  皮带速度: " + std::to_string(jointInfo.beltSpeed) + "米/秒");
    LogMessage("  摄像头距离: " + std::to_string(actualDistance) + "米");
    LogMessage("  安全冗余距离: " + std::to_string(safetyRedundancy) + "米");
    LogMessage("  有效距离: " + std::to_string(effectiveDistance) + "米");
    LogMessage("  计算延迟: " + std::to_string(delaySeconds) + "秒 (" + std::to_string(delayMs) + "毫秒)");
    LogMessage("  最佳距离参考: " + std::to_string(optimalDistance) + "米");

    return delayMs;
}

void JointCaptureController::ScheduleCapture(const JointInfo& jointInfo, int delayMs)
{
    // 停止之前的定时器
    if (m_captureTimer->IsRunning())
    {
        m_captureTimer->Stop();
        LogMessage("停止之前的拍照定时器");
    }

    // 保存待拍照的接头信息
    m_pendingJoint = jointInfo;

    // 启动定时器
    m_captureTimer->StartOnce(delayMs);
    LogMessage("安排拍照任务，延迟 " + std::to_string(delayMs) + " 毫秒");
}

void JointCaptureController::ExecuteCapture(const JointInfo& jointInfo)
{
    LogMessage("开始执行拍照，接头: " + jointInfo.jointNumber.ToStdString());

    // 创建必要的目录
    if (!m_photoStorageManager.CreateDirectories(jointInfo.detectTime))
    {
        SetError("创建照片目录失败");
        return;
    }

    // 生成照片路径
    wxString photoPath = m_photoStorageManager.GeneratePhotoPath(jointInfo.jointNumber, jointInfo.detectTime);

    // 执行拍照
    if (m_cameraManager.CapturePhoto(photoPath))
    {
        LogMessage("拍照成功: " + photoPath.ToStdString());
    }
    else
    {
        SetError("拍照失败: " + m_cameraManager.GetLastError());
    }
}

void JointCaptureController::SetError(const wxString& error)
{
    m_lastError = error;
    LogError(error.ToStdString());
}

void JointCaptureController::OnCaptureTimer(wxTimerEvent& event)
{
    LogMessage("定时器触发，执行拍照");
    ExecuteCapture(m_pendingJoint);
}
